#include "editor.h"
#include <QWheelEvent>

#include "component.h"
#include "base_type.h"

namespace Editor {
    constexpr int MAX_SIZE = 28000;  // 密尔
    Editor::Editor(QWidget* parent)
        : QGraphicsView(parent), scene_(nullptr){
        scene_ = new QGraphicsScene(this);
        scene_->setSceneRect(-MAX_SIZE, -MAX_SIZE, MAX_SIZE * 2, MAX_SIZE *2);
        this->setScene(scene_);
        
        // y 轴反向
        QTransform transform;
        transform.scale(1, -1);
        this->setTransform(transform);

        setRenderHint(QPainter::Antialiasing);
        setViewportUpdateMode(QGraphicsView::BoundingRectViewportUpdate);
        setCacheMode(QGraphicsView::CacheBackground);
    }

    Editor::~Editor() {
    }

    void Editor::Clear() {
        scene_->clear();
    }

    void Editor::AddItem(Item* item) {
        if (item) {
            scene_->addItem(item);
        }
    }

    Item *Editor::AddItem(const std::shared_ptr<DataBase::Component> &comp) {
        if (!comp) {
            return nullptr;
        }

        QPainterPath path;
        path.addPolygon(comp->GetPolygon());
        Item *item = new Item(DataBase::ItemType::Component, comp->GetLayer(), comp->GetName(), path);
        scene_->addItem(item);

        AddTextItem(item, comp->GetName(), comp->GetPosition());

        for (auto &pad : comp->GetPads()) {
            QPainterPath pad_path;
            pad_path.addPolygon(pad->polygon);
            Item *pad_item = new Item(DataBase::ItemType::Pad, comp->GetLayer(), 
                                    pad->name, pad_path, static_cast<QGraphicsItem*>(item));
            pad_item->setPos(pad->position);
        }

        return item;
    }

    void Editor::ResizeView() {
        if (scene_) {
            auto rect = scene_->itemsBoundingRect();
            fitInView(rect, Qt::KeepAspectRatio);
        }
    }

    void Editor::resizeEvent(QResizeEvent* event) {
        QGraphicsView::resizeEvent(event);
        ResizeView();
    }

    void Editor::wheelEvent(QWheelEvent* event) {
        qreal currentScale = transform().m11();
        
        // 计算缩放因子
        double factor = 1.0;
        if (event->angleDelta().y() > 0) {
            if (currentScale < 10.0) {
                factor = 1.1;
            } else {
                return;  
            }
        } else {
            if (currentScale > 0.1) {
                factor = 0.9;
            } else {
                return;
            }
        }
        
        // 设置缩放锚点为鼠标位置
        setTransformationAnchor(QGraphicsView::AnchorUnderMouse);
        
        // 应用缩放
        scale(factor, factor);
        
        event->accept();
    }

    void Editor::mouseMoveEvent(QMouseEvent* event) {
        QGraphicsView::mouseMoveEvent(event);
        auto pos = event->pos();
        auto scenePos = mapToScene(pos);
        emit SigMouseMove(scenePos);
    }

    void Editor::AddTextItem(QGraphicsItem *parent, const QString &text, const QPointF &pos) {
        QGraphicsTextItem *item = new QGraphicsTextItem(text, parent);
        item->setTransform(QTransform::fromScale(1, -1));
        item->setPos(pos);
    }
}