project(Editor)

find_package(Qt5 COMPONENTS Widgets REQUIRED)
find_package(Qt5 COMPONENTS Gui REQUIRED)

include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../DataBase)

add_library(${PROJECT_NAME} STATIC
    Editor.h    
    Editor.cpp
    Item.h
    Item.cpp
    brush_map.h
)

target_link_libraries(${PROJECT_NAME} PUBLIC
                                    Qt5::Widgets 
                                    Qt5::Gui
                                    DataBase
                                    )
