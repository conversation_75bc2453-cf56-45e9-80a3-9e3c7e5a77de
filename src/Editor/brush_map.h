#ifndef EDITOR_COLOR_MAP_H
#define EDITOR_COLOR_MAP_H

#include <QBrush>
#include <array>  // 添加这个包含

#include "base_type.h"

namespace Editor {
    class BrushMap {
    public:
        BrushMap() 
            : brush_map_{{
                // Layer::Top
                {
                    QBrush(QColor(255, 0, 0, 128)),     // Component - 红色填充
                    QBrush(QColor(0, 255, 0, 128)),     // Pad - 绿色填充
                    QBrush(Qt::NoBrush),           // Board - 无填充
                    QBrush(Qt::NoBrush)            // Keepout - 无填充
                },
                // Layer::Bottom
                {
                    QBrush(QColor(Qt::yellow)),
                    QBrush(QColor(Qt::green)),
                    QBrush(Qt::NoBrush),           // Board - 无填充
                    QBrush(Qt::NoBrush)            // Keepout - 无填充
                },
                // Layer::All
                {
                    QBrush(QColor(Qt::cyan)),
                    QBrush(QColor(Qt::magenta)),
                    QBrush(Qt::NoBrush),           // Board - 无填充
                    QBrush(Qt::NoBrush)            // Keepout - 无填充
                }
            }} {}

        ~BrushMap() = default;

        QBrush GetBrush(DataBase::Layer layer, DataBase::ItemType type) {
            int layer_idx = static_cast<int>(layer);
            int type_idx = static_cast<int>(type);
    
            if (layer_idx < 0 || layer_idx >= static_cast<int>(DataBase::Layer::Count) ||
                type_idx < 0 || type_idx >= static_cast<int>(DataBase::ItemType::Count)) {
                return Qt::NoBrush; 
            }

            return brush_map_[layer_idx][type_idx];
        }

    private:
        std::array<std::array<QBrush, static_cast<int>(DataBase::ItemType::Count)>, static_cast<int>(DataBase::Layer::Count)> brush_map_;
    };

}

#endif