#include "main_window.h"

#include <QStatusBar>
#include <QMenuBar>
#include <QFileDialog>
#include <QDockWidget>

#include "Item.h"

MainWindows::MainWindows(QWidget *parent) 
    :QMainWindow(parent) {
    InitUI();
}

void MainWindows::InitUI() {
    setWindowTitle("Layout Assistant");

    db_ = new DataBase::Database(this);
    
    editor_ = new Editor::Editor(this);
    setCentralWidget(editor_);
    
    // 添加状态栏
    status_label_ = new QLabel("Scene Coordinate: (0, 0)");
    statusBar()->addWidget(status_label_);

    CreateMenus();

    connect(db_, &DataBase::Database::SigInitDB, this, &MainWindows::OnInitDB);
    connect(editor_, &Editor::Editor::SigMouseMove, this, &MainWindows::OnMouseMove);
}


void MainWindows::CreateMenus() {
    QMenuBar *menu_bar = new QMenuBar(this);
    setMenuBar(menu_bar);

    QMenu *file_menu = menu_bar->addMenu(QStringLiteral("文件"));
    QAction *open_action = file_menu->addAction(QStringLiteral("打开"));
    QAction *save_action = file_menu->addAction(QStringLiteral("保存"));
    QAction *exit_action = file_menu->addAction(QStringLiteral("退出"));
    connect(open_action, &QAction::triggered, this, &MainWindows::OnOpenAction);
    connect(save_action, &QAction::triggered, this, &MainWindows::OnSaveAction);
    connect(exit_action, &QAction::triggered, this, &MainWindows::OnExitAction);

    QMenu *view_menu = menu_bar->addMenu(QStringLiteral("视图"));
    auto layout_view_menu = view_menu->addMenu(QStringLiteral("布局图"));
    auto wire_view_action = layout_view_menu->addAction(QStringLiteral("飞线"));
    wire_view_action->setCheckable(false);
    view_menu->addSeparator();
    CreateDockWidgetsAndActions(view_menu);

    QMenu *setting_menu = menu_bar->addMenu(QStringLiteral("设置"));

    QMenu *tool_menu = menu_bar->addMenu(QStringLiteral("工具"));
    QAction *auto_layout_action = tool_menu->addAction(QStringLiteral("自动布局"));
    QAction *legalization_action = tool_menu->addAction(QStringLiteral("布局合法化"));
}

void MainWindows::CreateDockWidgetsAndActions(QMenu *menu) {
    int dock_min_size = 200;
    QDockWidget *cost_dock = new QDockWidget(QStringLiteral("Cost曲线"), this);
    cost_dock->setMinimumSize(dock_min_size, dock_min_size);
    addDockWidget(Qt::BottomDockWidgetArea, cost_dock);
    dock_widgets_.append(cost_dock);

    QDockWidget *rule_dock = new QDockWidget(QStringLiteral("规则配置"), this);
    rule_dock->setMinimumSize(dock_min_size, dock_min_size);
    rule_config_widget_ = new RuleConfigWidget(rule_dock);
    rule_dock->setWidget(rule_config_widget_);
    addDockWidget(Qt::RightDockWidgetArea, rule_dock);
    dock_widgets_.append(rule_dock);

    QDockWidget *project_dock = new QDockWidget(QStringLiteral("项目浏览器"), this);
    project_dock->setMinimumSize(dock_min_size, dock_min_size);
    project_widget_ = new ProjectWidget(project_dock);
    project_dock->setWidget(project_widget_);
    addDockWidget(Qt::LeftDockWidgetArea, project_dock);
    dock_widgets_.append(project_dock);

    QDockWidget *attr_dock = new QDockWidget(QStringLiteral("属性窗口"), this);
    attr_dock->setMinimumSize(dock_min_size, dock_min_size);
    addDockWidget(Qt::RightDockWidgetArea, attr_dock);
    dock_widgets_.append(attr_dock);

    QDockWidget *log_dock = new QDockWidget(QStringLiteral("日志窗口"), this);
    log_dock->setMinimumSize(dock_min_size, dock_min_size);
    log_widget_ = new LogWidget(log_dock);
    log_dock->setWidget(log_widget_);
    addDockWidget(Qt::BottomDockWidgetArea, log_dock);
    dock_widgets_.append(log_dock);

    for (QDockWidget *dock : dock_widgets_) {
        QAction *action = menu->addAction(dock->windowTitle(), this, &MainWindows::OnToggleDockWidget);
        action->setCheckable(true);
        action->setChecked(dock->isVisible());
        action->setData(QVariant::fromValue(dock));
            
        connect(dock, &QDockWidget::visibilityChanged, action, &QAction::setChecked);
    }
}

void MainWindows::OnMouseMove(QPointF pos) {
    status_label_->setText(QString("Scene Coordinate: (%1, %2)").arg(pos.x()).arg(pos.y()));
}

void MainWindows::OnOpenAction() {
    QFileDialog dialog(this);
    dialog.setFileMode(QFileDialog::ExistingFile);
    if (dialog.exec() == QDialog::Accepted) {
        QString fileName = dialog.selectedFiles().first();
        if (fileName.endsWith(".json")) {
            current_file_ = fileName;
            db_->InitDB(fileName);
            project_widget_->AddComponents(db_->GetComponents().keys());
        }
    }
}

void MainWindows::OnSaveAction() {
    if (current_file_.isEmpty()) {
        // todo : 先断开与布局引擎的连接，再将数据写入到json中 
    }
}

void MainWindows::OnExitAction() {
    // todo : 先断开与布局引擎的连接，再保存数据
    close();
}

void MainWindows::InitLayoutEngine() {
    // todo : 初始化布局引擎
    // todo : 使用管道进行通信
}

void MainWindows::OnToggleDockWidget() {
    QAction *action = qobject_cast<QAction*>(sender());
    if (action) {
        QDockWidget *dock = qvariant_cast<QDockWidget*>(action->data());
        if (dock) {
            dock->setVisible(!dock->isVisible());
        }
    }
}

void MainWindows::OnInitDB() {
    auto components = db_->GetComponents();
    for (auto &comp : components) {
        if (comp) {
            Editor::Item *item = editor_->AddItem(comp);
            connect(item, &Editor::Item::SigPositionChanged, this, &MainWindows::OnItemPositionChanged);
            connect(item, &Editor::Item::SigSelectedChanged, this, &MainWindows::OnItemSelectedChanged);
        }
    }

    editor_->ResizeView();
}

void MainWindows::OnItemPositionChanged(const QString name, const QPointF pos) {
    db_->MoveComponent(name, pos);
    if (log_widget_) {
        log_widget_->AddLog(QString("Move %1 to (%2, %3)").arg(name).arg(pos.x()).arg(pos.y()));
    }
}

void MainWindows::OnItemSelectedChanged(const QString name, bool is_selected) {
    // update status bar
    if (is_selected) {
        status_label_->setText(QString("Selected: %1").arg(name));
    } 
}