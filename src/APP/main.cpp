#include <QApplication>
#include <QFile>
#include <QTextStream>
#include <QPushButton>

#include "main_window.h"

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);

    // QFile styleFile(":/style.qss");
    // if (styleFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
    //     QTextStream ts(&styleFile);
    //     a.setStyleSheet(ts.readAll());
    //     styleFile.close();
    // }
    
    MainWindows w;
    w.resize(1200, 800);
    w.show();

    return a.exec();
}