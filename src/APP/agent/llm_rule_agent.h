#ifndef APP_AGENT_LLM_RULE_AGENT
#define APP_AGENT_LLM_RULE_AGENT

#include <zmq.h>
#include <QJsonObject>

namespace Agent {
class LLMRuleAgent {
 public:
  LLMRuleAgent();
  ~LLMRuleAgent();

  QJsonObject GetNearRule(const QStringList& comp_list);
  void SetSchImagePath(const QString& image_path);
  bool CheckSchImagePath();

  QJsonObject TextToRule(const QString& text);

 private:
  void* context_;
  void* socket_;
  QString sch_image_path_;
  void CreateJsonBuf(const QString& opt, const QString& image_path,
                     const QStringList& comp_list, QString& buf);

  void ImageToBase64(const QString& image_path, QString& base64);

  bool SendBuffer(const QString& buf);
  QJsonObject RecvToJson();
};
}  // namespace Agent

#endif  // !APP_AGENT_LLM_RULE_AGENT
