#include "llm_rule_agent.h"

#include <QImage>
#include <QBuffer>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QFile>

#include <QMessageBox>

namespace Agent {
	const QString Task = "task";
	const QString Image = "image";
	const QString ComponentList = "component_list";
	const QString NearRule = "get_near_rule";
	const QString TextRule = "text_to_analysis";

	LLMRuleAgent::LLMRuleAgent() {
		context_ = zmq_ctx_new();
		socket_ = zmq_socket(context_, ZMQ_REQ);
		zmq_connect(socket_, "tcp://10.0.25.110:5555");
	}

	LLMRuleAgent::~LLMRuleAgent() {
		if (socket_) {
			zmq_close(socket_);
			socket_ = nullptr;
		}

		if (context_) {
			zmq_ctx_destroy(context_);
			context_ = nullptr;
		}
	}

	QJsonObject LLMRuleAgent::GetNearRule(const QStringList& comp_list) {
		QString buf;
		CreateJsonBuf(NearRule, sch_image_path_, comp_list, buf);

		SendBuffer(buf);
		return RecvToJson();
	}

	void LLMRuleAgent::SetSchImagePath(const QString& image_path) {
		sch_image_path_ = image_path;
	}

	bool LLMRuleAgent::CheckSchImagePath() {
		if (sch_image_path_.isEmpty()) {
			return false;
		}

		if (!QFile::exists(sch_image_path_)) {
			return false;
		}

        return true;
	}

	void LLMRuleAgent::CreateJsonBuf(const QString& opt, const QString& image_path,
		const QStringList& comp_list, QString& buf) {
		QJsonObject root;
		root[Task] = opt;

		QString base64;
		ImageToBase64(image_path, base64);
		root[Image] = std::move(base64);

		QJsonArray comp_array;
		for (auto& comp_name : comp_list) {
			comp_array.append(comp_name);
		}
		root[ComponentList] = std::move(comp_array);

		QJsonDocument doc(root);
		buf = doc.toJson(QJsonDocument::Compact);
	}

	void LLMRuleAgent::ImageToBase64(const QString& image_path, QString& base64) {
		QImage image(image_path);
		QByteArray ba;
		QBuffer buffer(&ba);
		buffer.open(QIODevice::WriteOnly);
		image.save(&buffer, "PNG");
		base64 = ba.toBase64();
	}

	bool LLMRuleAgent::SendBuffer(const QString& buf) {
		auto rc = zmq_send(socket_, buf.toStdString().c_str(), buf.size(), 0);
		return rc;
	}

	QJsonObject LLMRuleAgent::RecvToJson() {
		constexpr int buf_size = 4096;
		char buffer[buf_size];
		auto rc = zmq_recv(socket_, buffer, buf_size, 0);
		if (rc == -1) {
			return QJsonObject();
		}

		QByteArray ba(buffer, rc);
		QJsonDocument doc = QJsonDocument::fromJson(ba);
		return doc.object();
	}

	QJsonObject LLMRuleAgent::TextToRule(const QString &text) {
		QJsonObject root;
		root[Task] = TextRule;
		root[text] = text;

		QJsonDocument doc(root);
		auto buf = doc.toJson(QJsonDocument::Compact);

		auto rc = zmq_send(socket_, buf.toStdString().c_str(), buf.size(), 0);
		if (rc == -1) {
			return QJsonObject();
		}

		return RecvToJson();
	}
}