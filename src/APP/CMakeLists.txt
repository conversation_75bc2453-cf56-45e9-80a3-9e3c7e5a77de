find_package(Qt5 COMPONENTS Widgets REQUIRED)
find_package(Qt5 COMPONENTS Gui REQUIRED)
find_package(Qt5 COMPONENTS Core REQUIRED)
find_package(ZeroMQ CONFIG REQUIRED)

set(RESOURCES ../Resources/Resources.qrc)
qt5_add_resources(RESOURCES_RCC ${RESOURCES})

include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../Editor)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../DataBase)

add_executable(${PROJECT_NAME} WIN32
                                main.cpp
                                main_window.h
                                main_window.cpp
                                UI/log_widget.h
                                UI/log_widget.cpp
                                UI/project_widget.h
                                UI/project_widget.cpp
                                UI/rule_config_widget.h 
                                UI/rule_config_widget.cpp 
                                UI/rule_config_dialog.h 
                                UI/rule_config_dialog.cpp 
                                Agent/llm_rule_agent.h
                                Agent/llm_rule_agent.cpp
                                ${RESOURCES_RCC}
)

target_link_libraries(${PROJECT_NAME} PRIVATE
                                    Qt5::Widgets 
                                    Qt5::Gui 
                                    Qt5::Core
                                    Editor
                                    libzmq libzmq-static
                            )
