#include "rule_config_widget.h"

#include <QAbstractItemView>
#include <QFileDialog>
#include <QHBoxLayout>
#include <QJsonArray>
#include <QMessageBox>
#include <QPushButton>
#include <QSplitter>
#include <QVBoxLayout>

RuleConfigWidget::RuleConfigWidget(QWidget* parent) : QWidget(parent) {

  list_widget_ = new QListWidget;
  list_widget_->setSelectionMode(QAbstractItemView::MultiSelection);

  text_edit_ = new QTextEdit();
  analysis_btn_ = new QPushButton(QStringLiteral("分析"));
  image_btn_ = new QPushButton(QStringLiteral("上传原理图"));
  auto_layout_btn_ = new QPushButton(QStringLiteral("自动布局"));

  auto layout = new QVBoxLayout();
  layout->setContentsMargins(5, 5, 5, 5);  // 稍微增加边距使界面更舒适
  layout->setSpacing(5);

  QSplitter* splitter = new QSplitter(Qt::Vertical);
  splitter->addWidget(list_widget_);
  splitter->addWidget(text_edit_);
  splitter->setSizes(QList<int>() << 280 << 120);

  layout->addWidget(splitter, 7);

  QHBoxLayout* hbox = new QHBoxLayout();
  hbox->addWidget(analysis_btn_);
  hbox->addWidget(image_btn_);
  hbox->addWidget(auto_layout_btn_);

  // hbox->addStretch();

  layout->addLayout(hbox, 1);

  this->setLayout(layout);

  connect(list_widget_, &QListWidget::itemDoubleClicked, this,
          &RuleConfigWidget::OnItemDoubleClicked);
  connect(image_btn_, &QPushButton::clicked, this,
          &RuleConfigWidget::OnImageBtnClicked);
  connect(auto_layout_btn_, &QPushButton::clicked, this,
          &RuleConfigWidget::OnAutoLayoutBtnClicked);

  InitLLM();
  InitConfigList();
}

RuleConfigWidget::~RuleConfigWidget() {}

void RuleConfigWidget::InitConfigList() {
  rule_map_["near"] = {QStringLiteral("靠近规则"), []() -> ConfigDialogAPI* {
                         return new NearConfigDialog();
                       }};
  // rule_map_["far"] = {QStringLiteral("远离规则"), []() {return new FarConfigDialog("Far Config")} };

  for (auto it = rule_map_.constBegin(); it != rule_map_.constEnd(); it++) {
    auto* item = new QListWidgetItem(it.value().first);
    item->setCheckState(Qt::Unchecked);
    item->setData(Qt::UserRole, it.key());

    list_widget_->addItem(item);
  }
}

void RuleConfigWidget::InitLLM() {
  llm_agent_ = std::make_shared<Agent::LLMRuleAgent>();
}

void RuleConfigWidget::OnItemDoubleClicked(QListWidgetItem* item) {
  QString key = item->data(Qt::UserRole).toString();
  if (rule_map_.contains(key)) {
    // 使用QScopedPointer管理对话框生命周期
    QScopedPointer<ConfigDialogAPI> config_dialog(rule_map_[key].second());
    config_dialog->InitConfig(llm_agent_, config_[key].toObject());
    if (!llm_agent_->CheckSchImagePath()) {
      QMessageBox::warning(this, "提示", "请先上传原理图");
      return;
    }
    QDialog* dialog = dynamic_cast<QDialog*>(config_dialog.data());
    if (!dialog) {
      return;
    }

    int result = dialog->exec();

    if (result == QDialog::Accepted) {
      QJsonObject value = config_dialog->GetConfig();
      config_[key] = value[key];
    }
  }
}

void RuleConfigWidget::OnImageBtnClicked() {
  QStringList comp_list;
  for (int i = 0; i < list_widget_->count(); i++) {
    QListWidgetItem* item = list_widget_->item(i);
    if (item->checkState() == Qt::Checked) {
      comp_list.append(item->text());
    }
  }

  QString image_path = QFileDialog::getOpenFileName(
      this, "选择图片", "", "Image Files (*.png *.jpg *.bmp)");
  if (image_path.isEmpty()) {
    return;
  }

  if (llm_agent_) {
    llm_agent_->SetSchImagePath(image_path);
  }
}

void RuleConfigWidget::OnAnalysisBtnClicked() {
  if (!llm_agent_) {
    return;
  }

  auto text = text_edit_->toPlainText();
  if (text.isEmpty()) {
    return;
  }

  QJsonObject object = llm_agent_->TextToRule(text);

  if (object.contains("near")) {
    QJsonArray array = object["near"].toArray();
    config_["near"] = array;
  }
}

void RuleConfigWidget::OnAutoLayoutBtnClicked() {
  // OnAnalysisBtnClicked()
  // Auto Layout
}