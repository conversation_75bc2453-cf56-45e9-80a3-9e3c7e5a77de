#ifndef APP_MAIN_WINDOW_H
#define APP_MAIN_WINDOW_H

#include <QDockWidget>
#include <QLabel>
#include <QMainWindow>

#include "UI/log_widget.h"
#include "UI/project_widget.h"
#include "UI/rule_config_widget.h"
#include "database.h"
#include "editor.h"

class MainWindows : public QMainWindow {
  Q_OBJECT

 public:
  MainWindows(QWidget* parent = nullptr);
  ~MainWindows() = default;

 private:
  Editor::Editor* editor_;
  DataBase::Database* db_;

  QLabel* status_label_;
  QString current_file_;
  LogWidget* log_widget_;
  ProjectWidget* project_widget_;
  RuleConfigWidget* rule_config_widget_;
  QList<QDockWidget*> dock_widgets_;

  void InitUI();
  void CreateMenus();
  void CreateDockWidgetsAndActions(QMenu* menu);
  void InitLayoutEngine();

 private slots:
  void OnOpenAction();
  void OnSaveAction();
  void OnExitAction();
  void OnToggleDockWidget();
  void OnMouseMove(QPointF pos);
  void OnItemPositionChanged(const QString name, const QPointF pos);
  void OnItemSelectedChanged(const QString name, bool is_selected);

  void OnInitDB();
};

#endif