#ifndef DATA_BASE_NET_H
#define DATA_BASE_NET_H

#include "pad.h"

namespace DataBase
{
    class Net
    {
    public:
        Net(const QString &name) : name_(name) {};
        ~Net() = default;

        double CalcHPWL() const {
            double hpwl = 0.0;

            double min_x = std::numeric_limits<double>::max();
            double max_x = std::numeric_limits<double>::lowest();
            double min_y = std::numeric_limits<double>::max();
            double max_y = std::numeric_limits<double>::lowest();
            for(auto &pad : pads_) {
                if (!pad)   {
                    continue;
                }
                auto p = pad->position;
                if (p.x() < min_x)    min_x = p.x();
                if (p.x() > max_x)    max_x = p.x();
                if (p.y() < min_y)    min_y = p.y();
                if (p.y() > max_y)    max_y = p.y();
            }
            hpwl = (max_x - min_x) + (max_y - min_y);
            return hpwl;
        }

        QString GetName() const { return name_; }
        void AddPad(std::shared_ptr<Pad> pad) { pads_.append(pad); }

    private:
        QString name_;
        QVector<std::shared_ptr<Pad>> pads_;
    };

} // namespace DataBase


#endif