#include "safe_zmq.h"
#include <QJsonParseError>

namespace MQ {

SafeZmqClient::SafeZmqClient(const QString& endpoint, int timeout_ms)
    : context_(nullptr)
    , socket_(nullptr)
    , endpoint_(endpoint)
    , timeout_ms_(timeout_ms) {
    
    buffer_.reserve(4096);  // 预分配4KB缓冲区
    Initialize();
}

SafeZmqClient::~SafeZmqClient() {
    Cleanup();
}

void SafeZmqClient::SendString(const QString& message) {
    if (!socket_) {
        throw ZmqException("Socket not initialized");
    }
    
    QByteArray data = message.toUtf8();
    int result = zmq_send(socket_, data.constData(), data.size(), 0);
    CheckResult(result, "send");
}

QString SafeZmqClient::ReceiveString() {
    if (!socket_) {
        throw ZmqException("Socket not initialized");
    }
    
    // 使用动态缓冲区接收消息
    EnsureBufferSize(1024);  // 确保至少1KB
    
    int received = zmq_recv(socket_, buffer_.data(), buffer_.size(), 0);
    CheckResult(received, "receive");
    
    // 如果缓冲区不够大，扩展并重试
    if (received == static_cast<int>(buffer_.size())) {
        // 可能消息被截断，扩展缓冲区
        EnsureBufferSize(buffer_.size() * 2);
        received = zmq_recv(socket_, buffer_.data(), buffer_.size(), 0);
        CheckResult(received, "receive (retry)");
    }
    
    return QString::fromUtf8(buffer_.data(), received);
}

void SafeZmqClient::SendJson(const QJsonObject& json) {
    QJsonDocument doc(json);
    QByteArray data = doc.toJson(QJsonDocument::Compact);
    QString json_string = QString::fromUtf8(data);
    SendString(json_string);
}

QJsonObject SafeZmqClient::ReceiveJson() {
    QString json_string = ReceiveString();
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(json_string.toUtf8(), &error);
    
    if (error.error != QJsonParseError::NoError) {
        throw ZmqException("Failed to parse JSON: " + error.errorString().toStdString());
    }
    
    if (!doc.isObject()) {
        throw ZmqException("Received JSON is not an object");
    }
    
    return doc.object();
}

QString SafeZmqClient::SendRequest(const QString& request) {
    SendString(request);
    return ReceiveString();
}

QJsonObject SafeZmqClient::SendRequest(const QJsonObject& request) {
    SendJson(request);
    return ReceiveJson();
}

bool SafeZmqClient::IsConnected() const {
    return socket_ != nullptr;
}

void SafeZmqClient::SetTimeout(int timeout_ms) {
    timeout_ms_ = timeout_ms;
    
    if (socket_) {
        // 设置发送和接收超时
        zmq_setsockopt(socket_, ZMQ_SNDTIMEO, &timeout_ms_, sizeof(timeout_ms_));
        zmq_setsockopt(socket_, ZMQ_RCVTIMEO, &timeout_ms_, sizeof(timeout_ms_));
    }
}

void SafeZmqClient::Reconnect() {
    Cleanup();
    Initialize();
}

void SafeZmqClient::Initialize() {
    // 创建context
    context_ = zmq_ctx_new();
    if (!context_) {
        throw ZmqException("Failed to create ZMQ context");
    }
    
    // 创建socket
    socket_ = zmq_socket(context_, ZMQ_REQ);
    if (!socket_) {
        zmq_ctx_destroy(context_);
        context_ = nullptr;
        throw ZmqException("Failed to create ZMQ socket");
    }
    
    // 设置超时
    SetTimeout(timeout_ms_);
    
    // 设置linger时间，确保优雅关闭
    int linger = 1000;  // 1秒
    zmq_setsockopt(socket_, ZMQ_LINGER, &linger, sizeof(linger));
    
    // 连接到端点
    QByteArray endpoint_data = endpoint_.toUtf8();
    int result = zmq_connect(socket_, endpoint_data.constData());
    CheckResult(result, "connect");
}

void SafeZmqClient::Cleanup() {
    if (socket_) {
        zmq_close(socket_);
        socket_ = nullptr;
    }
    
    if (context_) {
        zmq_ctx_destroy(context_);
        context_ = nullptr;
    }
}

void SafeZmqClient::CheckResult(int result, const QString& operation) {
    if (result == -1) {
        int error_code = zmq_errno();
        QString error_msg = QString("ZMQ %1 failed: %2 (error %3)")
                           .arg(operation)
                           .arg(zmq_strerror(error_code))
                           .arg(error_code);
        
        // 特殊处理超时错误
        if (error_code == EAGAIN) {
            error_msg = QString("ZMQ %1 timeout after %2ms").arg(operation).arg(timeout_ms_);
        }
        
        throw ZmqException(error_msg.toStdString());
    }
}

void SafeZmqClient::EnsureBufferSize(size_t min_size) {
    if (buffer_.size() < min_size) {
        // 限制最大缓冲区大小为10MB，防止内存耗尽
        size_t max_size = 10 * 1024 * 1024;
        size_t new_size = std::min(min_size, max_size);
        buffer_.resize(new_size);
    }
}

std::unique_ptr<SafeZmqClient> CreateLLMClient(const QString& endpoint) {
    return std::make_unique<SafeZmqClient>(endpoint, 30000);  // 30秒超时，适合LLM响应
}

} // namespace MQ
