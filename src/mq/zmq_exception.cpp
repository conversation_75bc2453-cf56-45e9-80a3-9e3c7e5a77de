#include "zmq_exception.h"
#include <sstream>

namespace MQ {

ZmqException::ZmqException(const std::string& message, int error_code)
    : std::runtime_error(message), error_code_(error_code) {
}

std::string ZmqException::GetZmqErrorString() const {
    return std::string(zmq_strerror(error_code_));
}

std::string ZmqException::GetFullErrorMessage() const {
    std::ostringstream oss;
    oss << what() << " (ZMQ Error " << error_code_ << ": " << GetZmqErrorString() << ")";
    return oss.str();
}

ZmqConnectionException::ZmqConnectionException(const std::string& endpoint, int error_code)
    : ZmqException("Failed to connect to endpoint: " + endpoint, error_code) {
}

ZmqSendException::ZmqSendException(const std::string& message, int error_code)
    : ZmqException(message, error_code) {
}

ZmqReceiveException::ZmqReceiveException(const std::string& message, int error_code)
    : ZmqException(message, error_code) {
}

ZmqTimeoutException::ZmqTimeoutException(const std::string& operation, int timeout_ms)
    : ZmqException("Operation '" + operation + "' timed out after " + 
                   std::to_string(timeout_ms) + "ms", EAGAIN) {
}

ZmqConfigException::ZmqConfigException(const std::string& message)
    : ZmqException("Configuration error: " + message, 0) {
}

ZmqResourceException::ZmqResourceException(const std::string& resource_type, int error_code)
    : ZmqException("Failed to create " + resource_type, error_code) {
}

void CheckZmqResult(int result, const std::string& operation) {
    if (result == -1) {
        int error_code = zmq_errno();
        
        // 根据错误类型抛出特定异常
        switch (error_code) {
            case EAGAIN:
                throw ZmqTimeoutException(operation, 0);
            case ECONNREFUSED:
            case EHOSTUNREACH:
            case ENETUNREACH:
                throw ZmqConnectionException("Connection failed during " + operation, error_code);
            default:
                throw ZmqException("Operation '" + operation + "' failed", error_code);
        }
    }
}

void CheckZmqPointer(void* ptr, const std::string& resource_type) {
    if (ptr == nullptr) {
        throw ZmqResourceException(resource_type);
    }
}

} // namespace MQ
