#include "safe_zmq_socket.h"
#include "zmq_exception.h"
#include <thread>
#include <chrono>
#include <algorithm>

namespace MQ {

SafeZmqSocket::SafeZmqSocket(std::shared_ptr<SafeZmqContext> context, const ZmqConfig& config)
    : context_(std::move(context))
    , socket_(nullptr)
    , config_(config)
    , is_connected_(false)
    , is_closed_(false) {
    
    config_.Validate();
    Initialize();
}

SafeZmqSocket::~SafeZmqSocket() {
    Cleanup();
}

SafeZmqSocket::SafeZmqSocket(SafeZmqSocket&& other) noexcept
    : context_(std::move(other.context_))
    , socket_(other.socket_)
    , config_(std::move(other.config_))
    , is_connected_(other.is_connected_.load())
    , is_closed_(other.is_closed_.load())
    , receive_buffer_(std::move(other.receive_buffer_)) {
    
    other.socket_ = nullptr;
    other.is_connected_.store(false);
    other.is_closed_.store(true);
}

SafeZmqSocket& SafeZmqSocket::operator=(SafeZmqSocket&& other) noexcept {
    if (this != &other) {
        Cleanup();
        
        context_ = std::move(other.context_);
        socket_ = other.socket_;
        config_ = std::move(other.config_);
        is_connected_.store(other.is_connected_.load());
        is_closed_.store(other.is_closed_.load());
        receive_buffer_ = std::move(other.receive_buffer_);
        
        other.socket_ = nullptr;
        other.is_connected_.store(false);
        other.is_closed_.store(true);
    }
    return *this;
}

void SafeZmqSocket::Connect() {
    std::lock_guard<std::mutex> lock(mutex_);
    if (is_closed_.load()) {
        throw ZmqException("Cannot connect: socket is closed");
    }
    
    if (is_connected_.load()) {
        return;  // 已经连接
    }
    
    auto connect_operation = [this]() {
        int result = zmq_connect(socket_, config_.GetEndpoint().c_str());
        CheckZmqResult(result, "zmq_connect");
        return result;
    };
    
    WithRetry(connect_operation, "connect");
    is_connected_.store(true);
}

void SafeZmqSocket::Bind() {
    std::lock_guard<std::mutex> lock(mutex_);
    if (is_closed_.load()) {
        throw ZmqException("Cannot bind: socket is closed");
    }
    
    if (is_connected_.load()) {
        return;  // 已经绑定
    }
    
    int result = zmq_bind(socket_, config_.GetEndpoint().c_str());
    CheckZmqResult(result, "zmq_bind");
    is_connected_.store(true);
}

void SafeZmqSocket::Disconnect() {
    std::lock_guard<std::mutex> lock(mutex_);
    if (is_connected_.load()) {
        zmq_disconnect(socket_, config_.GetEndpoint().c_str());
        is_connected_.store(false);
    }
}

void SafeZmqSocket::Close() {
    std::lock_guard<std::mutex> lock(mutex_);
    if (!is_closed_.load()) {
        Disconnect();
        is_closed_.store(true);
    }
}

size_t SafeZmqSocket::Send(const void* data, size_t size, int flags) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (is_closed_.load()) {
        throw ZmqSendException("Cannot send: socket is closed");
    }
    
    if (!is_connected_.load()) {
        Connect();
    }
    
    auto send_operation = [this, data, size, flags]() {
        int result = zmq_send(socket_, data, size, flags);
        if (result == -1) {
            CheckZmqResult(-1, "zmq_send");
        }
        return result;
    };
    
    return WithRetry(send_operation, "send");
}

size_t SafeZmqSocket::Send(const std::string& message, int flags) {
    return Send(message.data(), message.size(), flags);
}

void SafeZmqSocket::SendMultipart(const std::vector<std::string>& parts) {
    if (parts.empty()) {
        throw ZmqSendException("Cannot send empty multipart message");
    }
    
    for (size_t i = 0; i < parts.size(); ++i) {
        int flags = (i < parts.size() - 1) ? ZMQ_SNDMORE : 0;
        Send(parts[i], flags);
    }
}

size_t SafeZmqSocket::Receive(void* buffer, size_t max_size, int flags) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (is_closed_.load()) {
        throw ZmqReceiveException("Cannot receive: socket is closed");
    }
    
    if (!is_connected_.load()) {
        Connect();
    }
    
    auto receive_operation = [this, buffer, max_size, flags]() {
        int result = zmq_recv(socket_, buffer, max_size, flags);
        if (result == -1) {
            CheckZmqResult(-1, "zmq_recv");
        }
        return result;
    };
    
    return WithRetry(receive_operation, "receive");
}

std::string SafeZmqSocket::ReceiveString(int flags) {
    auto buffer = ReceiveBuffer(flags);
    return std::string(buffer.begin(), buffer.end());
}

std::vector<char> SafeZmqSocket::ReceiveBuffer(int flags) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (is_closed_.load()) {
        throw ZmqReceiveException("Cannot receive: socket is closed");
    }
    
    if (!is_connected_.load()) {
        Connect();
    }
    
    // 首先尝试接收到预分配的缓冲区
    EnsureBufferSize(config_.GetMaxBufferSize());
    
    auto receive_operation = [this, flags]() {
        int result = zmq_recv(socket_, receive_buffer_.data(), receive_buffer_.size(), flags);
        if (result == -1) {
            CheckZmqResult(-1, "zmq_recv");
        }
        return result;
    };
    
    size_t received_size = WithRetry(receive_operation, "receive");
    
    // 返回实际接收的数据
    std::vector<char> result(receive_buffer_.begin(), receive_buffer_.begin() + received_size);
    return result;
}

std::vector<std::string> SafeZmqSocket::ReceiveMultipart() {
    std::vector<std::string> parts;
    
    do {
        parts.push_back(ReceiveString());
    } while (HasMore());
    
    return parts;
}

bool SafeZmqSocket::HasMore() const {
    return GetOptionInt(ZMQ_RCVMORE) == 1;
}

void SafeZmqSocket::SetOption(int option, int value) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (is_closed_.load()) {
        throw ZmqException("Cannot set option: socket is closed");
    }
    
    int result = zmq_setsockopt(socket_, option, &value, sizeof(value));
    CheckZmqResult(result, "zmq_setsockopt");
}

void SafeZmqSocket::SetOption(int option, const std::string& value) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (is_closed_.load()) {
        throw ZmqException("Cannot set option: socket is closed");
    }
    
    int result = zmq_setsockopt(socket_, option, value.c_str(), value.size());
    CheckZmqResult(result, "zmq_setsockopt");
}

int SafeZmqSocket::GetOptionInt(int option) const {
    std::lock_guard<std::mutex> lock(mutex_);
    if (is_closed_.load()) {
        throw ZmqException("Cannot get option: socket is closed");
    }
    
    int value;
    size_t size = sizeof(value);
    int result = zmq_getsockopt(socket_, option, &value, &size);
    CheckZmqResult(result, "zmq_getsockopt");
    return value;
}

std::string SafeZmqSocket::GetOptionString(int option) const {
    std::lock_guard<std::mutex> lock(mutex_);
    if (is_closed_.load()) {
        throw ZmqException("Cannot get option: socket is closed");
    }
    
    char buffer[256];
    size_t size = sizeof(buffer);
    int result = zmq_getsockopt(socket_, option, buffer, &size);
    CheckZmqResult(result, "zmq_getsockopt");
    return std::string(buffer, size);
}

bool SafeZmqSocket::IsValid() const noexcept {
    return socket_ != nullptr && !is_closed_.load();
}

bool SafeZmqSocket::IsConnected() const noexcept {
    return is_connected_.load() && !is_closed_.load();
}

void SafeZmqSocket::Reconnect() {
    std::lock_guard<std::mutex> lock(mutex_);
    if (is_connected_.load()) {
        Disconnect();
    }
    Connect();
}

bool SafeZmqSocket::WaitForRead(int timeout_ms) const {
    return Poll(ZMQ_POLLIN, timeout_ms) & ZMQ_POLLIN;
}

bool SafeZmqSocket::WaitForWrite(int timeout_ms) const {
    return Poll(ZMQ_POLLOUT, timeout_ms) & ZMQ_POLLOUT;
}

void SafeZmqSocket::Initialize() {
    if (!context_ || !context_->IsValid()) {
        throw ZmqResourceException("Invalid ZMQ Context");
    }
    
    socket_ = zmq_socket(context_->GetContext(), static_cast<int>(config_.GetSocketType()));
    CheckZmqPointer(socket_, "ZMQ Socket");
    
    // 通知context增加socket计数
    context_->IncrementSocketCount();
    
    try {
        ApplyConfig();
        EnsureBufferSize(1024);  // 初始缓冲区大小
    } catch (...) {
        Cleanup();
        throw;
    }
}

void SafeZmqSocket::ApplyConfig() {
    // 设置超时
    if (config_.GetSendTimeoutMs() >= 0) {
        SetOption(ZMQ_SNDTIMEO, config_.GetSendTimeoutMs());
    }
    if (config_.GetReceiveTimeoutMs() >= 0) {
        SetOption(ZMQ_RCVTIMEO, config_.GetReceiveTimeoutMs());
    }
    
    // 设置其他选项
    SetOption(ZMQ_LINGER, config_.GetLingerMs());
    SetOption(ZMQ_SNDHWM, config_.GetHighWaterMark());
    SetOption(ZMQ_RCVHWM, config_.GetHighWaterMark());
    
    // 应用自定义选项
    int int_value;
    std::string string_value;
    
    // 这里可以遍历配置中的自定义选项并应用
    // 由于ZmqConfig类中的选项是私有的，这里简化处理
}

void SafeZmqSocket::Cleanup() {
    if (socket_) {
        Close();
        zmq_close(socket_);
        socket_ = nullptr;
        
        // 通知context减少socket计数
        if (context_) {
            context_->DecrementSocketCount();
        }
    }
}

template<typename Func>
auto SafeZmqSocket::WithRetry(Func&& operation, const std::string& operation_name) 
    -> decltype(operation()) {
    
    int retries = 0;
    int max_retries = config_.GetMaxRetries();
    
    while (true) {
        try {
            return operation();
        } catch (const ZmqTimeoutException&) {
            throw;  // 超时异常不重试
        } catch (const ZmqException& e) {
            if (retries >= max_retries || !config_.IsAutoReconnect()) {
                throw;
            }
            
            ++retries;
            std::this_thread::sleep_for(std::chrono::milliseconds(config_.GetRetryIntervalMs()));
            
            // 尝试重连
            try {
                Reconnect();
            } catch (...) {
                if (retries >= max_retries) {
                    throw;
                }
            }
        }
    }
}

void SafeZmqSocket::EnsureBufferSize(size_t required_size) {
    if (receive_buffer_.size() < required_size) {
        size_t new_size = std::min(required_size, config_.GetMaxBufferSize());
        receive_buffer_.resize(new_size);
    }
}

int SafeZmqSocket::Poll(int events, int timeout_ms) const {
    zmq_pollitem_t item = { socket_, 0, static_cast<short>(events), 0 };
    int result = zmq_poll(&item, 1, timeout_ms);
    
    if (result == -1) {
        CheckZmqResult(-1, "zmq_poll");
    }
    
    return item.revents;
}

} // namespace MQ
