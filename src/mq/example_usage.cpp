/**
 * @file example_usage.cpp
 * @brief 展示如何使用SafeZmqClient替代现有的LLMRuleAgent
 * 
 * 这个示例展示了如何将现有的不安全的ZeroMQ代码迁移到新的安全封装
 */

#include "safe_zmq.h"
#include <QJsonObject>
#include <QJsonArray>
#include <QStringList>
#include <QDebug>
#include <iostream>

// 模拟现有的LLMRuleAgent功能
class SafeLLMRuleAgent {
public:
    SafeLLMRuleAgent(const QString& endpoint = "tcp://10.0.25.110:5555") {
        try {
            // 使用新的安全客户端
            client_ = MQ::CreateLLMClient(endpoint);
            qDebug() << "SafeLLMRuleAgent initialized successfully";
        } catch (const MQ::ZmqException& e) {
            qDebug() << "Failed to initialize SafeLLMRuleAgent:" << e.what();
            throw;
        }
    }
    
    /**
     * @brief 获取近邻规则（替代原有的GetNearRule方法）
     * @param comp_list 组件列表
     * @return 规则JSON对象
     */
    QJsonObject GetNearRule(const QStringList& comp_list) {
        try {
            // 构建请求JSON
            QJsonObject request;
            request["task"] = "get_near_rule";
            request["image"] = sch_image_path_;
            
            QJsonArray components;
            for (const QString& comp : comp_list) {
                components.append(comp);
            }
            request["component_list"] = components;
            
            // 发送请求并接收响应
            return client_->SendRequest(request);
            
        } catch (const MQ::ZmqException& e) {
            qDebug() << "GetNearRule failed:" << e.what();
            return QJsonObject();  // 返回空对象表示失败
        }
    }
    
    /**
     * @brief 文本转规则（替代原有的TextToRule方法）
     * @param text 输入文本
     * @return 规则JSON对象
     */
    QJsonObject TextToRule(const QString& text) {
        try {
            QJsonObject request;
            request["task"] = "text_to_analysis";
            request["text"] = text;
            
            return client_->SendRequest(request);
            
        } catch (const MQ::ZmqException& e) {
            qDebug() << "TextToRule failed:" << e.what();
            return QJsonObject();
        }
    }
    
    /**
     * @brief 设置原理图图像路径
     * @param image_path 图像路径
     */
    void SetSchImagePath(const QString& image_path) {
        sch_image_path_ = image_path;
    }
    
    /**
     * @brief 检查原理图图像路径是否有效
     * @return true如果路径有效
     */
    bool CheckSchImagePath() const {
        return !sch_image_path_.isEmpty();
    }
    
    /**
     * @brief 设置超时时间
     * @param timeout_ms 超时时间（毫秒）
     */
    void SetTimeout(int timeout_ms) {
        if (client_) {
            client_->SetTimeout(timeout_ms);
        }
    }
    
    /**
     * @brief 重新连接
     */
    void Reconnect() {
        if (client_) {
            try {
                client_->Reconnect();
                qDebug() << "Reconnected successfully";
            } catch (const MQ::ZmqException& e) {
                qDebug() << "Reconnection failed:" << e.what();
            }
        }
    }

private:
    std::unique_ptr<MQ::SafeZmqClient> client_;
    QString sch_image_path_;
};

// 使用示例
void ExampleUsage() {
    try {
        // 创建安全的LLM规则代理
        SafeLLMRuleAgent agent("tcp://10.0.25.110:5555");
        
        // 设置图像路径
        agent.SetSchImagePath("/path/to/schematic.png");
        
        // 设置30秒超时（适合LLM响应）
        agent.SetTimeout(30000);
        
        // 示例1：获取近邻规则
        QStringList components = {"R1", "C1", "U1"};
        QJsonObject near_rules = agent.GetNearRule(components);
        
        if (!near_rules.isEmpty()) {
            qDebug() << "Near rules received:" << near_rules;
        } else {
            qDebug() << "Failed to get near rules";
        }
        
        // 示例2：文本转规则
        QString text = "电阻R1和电容C1之间的距离应该小于5mm";
        QJsonObject text_rules = agent.TextToRule(text);
        
        if (!text_rules.isEmpty()) {
            qDebug() << "Text rules received:" << text_rules;
        } else {
            qDebug() << "Failed to convert text to rules";
        }
        
    } catch (const MQ::ZmqException& e) {
        std::cerr << "ZMQ Error: " << e.what() << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
    }
}

// 简单的性能测试
void PerformanceTest() {
    try {
        SafeLLMRuleAgent agent;
        agent.SetTimeout(5000);  // 5秒超时
        
        const int test_count = 10;
        QStringList test_components = {"R1", "R2", "C1"};
        
        qDebug() << "Starting performance test with" << test_count << "requests...";
        
        auto start_time = std::chrono::high_resolution_clock::now();
        
        int success_count = 0;
        for (int i = 0; i < test_count; ++i) {
            QJsonObject result = agent.GetNearRule(test_components);
            if (!result.isEmpty()) {
                success_count++;
            }
        }
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        qDebug() << "Performance test completed:";
        qDebug() << "- Total requests:" << test_count;
        qDebug() << "- Successful requests:" << success_count;
        qDebug() << "- Total time:" << duration.count() << "ms";
        qDebug() << "- Average time per request:" << (duration.count() / test_count) << "ms";
        
    } catch (const std::exception& e) {
        qDebug() << "Performance test failed:" << e.what();
    }
}

/**
 * 迁移指南：
 * 
 * 1. 替换头文件：
 *    #include "llm_rule_agent.h" -> #include "safe_zmq.h"
 * 
 * 2. 替换类名：
 *    Agent::LLMRuleAgent -> SafeLLMRuleAgent
 * 
 * 3. 错误处理：
 *    原来：检查返回值或空对象
 *    现在：使用try-catch捕获MQ::ZmqException
 * 
 * 4. 资源管理：
 *    原来：手动管理context和socket
 *    现在：自动RAII管理，无需手动清理
 * 
 * 5. 缓冲区安全：
 *    原来：固定4096字节缓冲区，可能溢出
 *    现在：动态缓冲区，自动扩展，最大10MB限制
 * 
 * 6. 超时控制：
 *    原来：无超时控制
 *    现在：可配置的发送/接收超时
 */
