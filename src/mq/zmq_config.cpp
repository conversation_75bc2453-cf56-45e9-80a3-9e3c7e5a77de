#include "zmq_config.h"
#include "zmq_exception.h"
#include <algorithm>

namespace MQ {

ZmqConfig::ZmqConfig()
    : endpoint_("tcp://localhost:5555")
    , socket_type_(SocketType::Request)
    , send_timeout_ms_(5000)
    , receive_timeout_ms_(5000)
    , connection_timeout_ms_(3000)
    , linger_ms_(1000)
    , high_water_mark_(1000)
    , max_buffer_size_(1024 * 1024)  // 1MB
    , max_retries_(3)
    , retry_interval_ms_(1000)
    , auto_reconnect_(true) {
}

ZmqConfig::ZmqConfig(const std::string& endpoint, SocketType socket_type)
    : ZmqConfig() {
    endpoint_ = endpoint;
    socket_type_ = socket_type;
}

ZmqConfig& ZmqConfig::SetEndpoint(const std::string& endpoint) {
    endpoint_ = endpoint;
    return *this;
}

ZmqConfig& ZmqConfig::SetSocketType(SocketType socket_type) {
    socket_type_ = socket_type;
    return *this;
}

ZmqConfig& ZmqConfig::SetSendTimeout(int timeout_ms) {
    send_timeout_ms_ = std::max(0, timeout_ms);
    return *this;
}

ZmqConfig& ZmqConfig::SetReceiveTimeout(int timeout_ms) {
    receive_timeout_ms_ = std::max(0, timeout_ms);
    return *this;
}

ZmqConfig& ZmqConfig::SetConnectionTimeout(int timeout_ms) {
    connection_timeout_ms_ = std::max(0, timeout_ms);
    return *this;
}

ZmqConfig& ZmqConfig::SetLinger(int linger_ms) {
    linger_ms_ = std::max(0, linger_ms);
    return *this;
}

ZmqConfig& ZmqConfig::SetHighWaterMark(int hwm) {
    high_water_mark_ = std::max(0, hwm);
    return *this;
}

ZmqConfig& ZmqConfig::SetMaxBufferSize(size_t size) {
    max_buffer_size_ = std::max(size_t(1024), size);  // 最小1KB
    return *this;
}

ZmqConfig& ZmqConfig::SetMaxRetries(int retries) {
    max_retries_ = std::max(0, retries);
    return *this;
}

ZmqConfig& ZmqConfig::SetRetryInterval(int interval_ms) {
    retry_interval_ms_ = std::max(100, interval_ms);  // 最小100ms
    return *this;
}

ZmqConfig& ZmqConfig::SetAutoReconnect(bool enable) {
    auto_reconnect_ = enable;
    return *this;
}

ZmqConfig& ZmqConfig::SetSocketOption(int option, int value) {
    int_options_[option] = value;
    return *this;
}

ZmqConfig& ZmqConfig::SetSocketOption(int option, const std::string& value) {
    string_options_[option] = value;
    return *this;
}

bool ZmqConfig::GetSocketOptionInt(int option, int& value) const {
    auto it = int_options_.find(option);
    if (it != int_options_.end()) {
        value = it->second;
        return true;
    }
    return false;
}

bool ZmqConfig::GetSocketOptionString(int option, std::string& value) const {
    auto it = string_options_.find(option);
    if (it != string_options_.end()) {
        value = it->second;
        return true;
    }
    return false;
}

void ZmqConfig::Validate() const {
    if (endpoint_.empty()) {
        throw ZmqConfigException("Endpoint cannot be empty");
    }
    
    // 检查端点格式
    if (endpoint_.find("://") == std::string::npos) {
        throw ZmqConfigException("Invalid endpoint format: " + endpoint_);
    }
    
    // 检查超时值
    if (send_timeout_ms_ < 0 || receive_timeout_ms_ < 0 || connection_timeout_ms_ < 0) {
        throw ZmqConfigException("Timeout values cannot be negative");
    }
    
    // 检查缓冲区大小
    if (max_buffer_size_ == 0) {
        throw ZmqConfigException("Buffer size cannot be zero");
    }
}

ZmqConfig ZmqConfig::CreateClientConfig(const std::string& endpoint) {
    return ZmqConfig(endpoint, SocketType::Request)
        .SetSendTimeout(5000)
        .SetReceiveTimeout(5000)
        .SetAutoReconnect(true)
        .SetMaxRetries(3);
}

ZmqConfig ZmqConfig::CreateServerConfig(const std::string& endpoint) {
    return ZmqConfig(endpoint, SocketType::Reply)
        .SetSendTimeout(5000)
        .SetReceiveTimeout(-1)  // 服务器通常不设置接收超时
        .SetAutoReconnect(false)
        .SetMaxRetries(0);
}

ZmqConfig ZmqConfig::CreatePublisherConfig(const std::string& endpoint) {
    return ZmqConfig(endpoint, SocketType::Publisher)
        .SetSendTimeout(1000)
        .SetReceiveTimeout(0)   // 发布者不接收消息
        .SetHighWaterMark(10000)
        .SetAutoReconnect(false);
}

ZmqConfig ZmqConfig::CreateSubscriberConfig(const std::string& endpoint, 
                                           const std::string& topic) {
    auto config = ZmqConfig(endpoint, SocketType::Subscriber)
        .SetSendTimeout(0)      // 订阅者不发送消息
        .SetReceiveTimeout(-1)
        .SetAutoReconnect(true)
        .SetMaxRetries(5);
    
    if (!topic.empty()) {
        config.SetSocketOption(ZMQ_SUBSCRIBE, topic);
    }
    
    return config;
}

} // namespace MQ
