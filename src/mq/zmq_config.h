#ifndef MQ_ZMQ_CONFIG_H
#define MQ_ZMQ_CONFIG_H

#include <string>
#include <chrono>
#include <map>
#include <zmq.h>

namespace MQ {

/**
 * @brief ZeroMQ套接字类型枚举
 */
enum class SocketType {
    Request = ZMQ_REQ,      // 请求套接字
    Reply = ZMQ_REP,        // 应答套接字
    Publisher = ZMQ_PUB,    // 发布套接字
    Subscriber = ZMQ_SUB,   // 订阅套接字
    Push = ZMQ_PUSH,        // 推送套接字
    Pull = ZMQ_PULL,        // 拉取套接字
    Dealer = ZMQ_DEALER,    // 经销商套接字
    Router = ZMQ_ROUTER     // 路由套接字
};

/**
 * @brief ZeroMQ配置类
 * 
 * 管理ZeroMQ连接的各种配置参数，包括超时、重试、缓冲区大小等
 */
class ZmqConfig {
public:
    /**
     * @brief 默认构造函数，使用默认配置
     */
    ZmqConfig();
    
    /**
     * @brief 构造函数
     * @param endpoint 连接端点
     * @param socket_type 套接字类型
     */
    ZmqConfig(const std::string& endpoint, SocketType socket_type);
    
    // Getter方法
    const std::string& GetEndpoint() const { return endpoint_; }
    SocketType GetSocketType() const { return socket_type_; }
    int GetSendTimeoutMs() const { return send_timeout_ms_; }
    int GetReceiveTimeoutMs() const { return receive_timeout_ms_; }
    int GetConnectionTimeoutMs() const { return connection_timeout_ms_; }
    int GetLingerMs() const { return linger_ms_; }
    int GetHighWaterMark() const { return high_water_mark_; }
    size_t GetMaxBufferSize() const { return max_buffer_size_; }
    int GetMaxRetries() const { return max_retries_; }
    int GetRetryIntervalMs() const { return retry_interval_ms_; }
    bool IsAutoReconnect() const { return auto_reconnect_; }
    
    // Setter方法（支持链式调用）
    ZmqConfig& SetEndpoint(const std::string& endpoint);
    ZmqConfig& SetSocketType(SocketType socket_type);
    ZmqConfig& SetSendTimeout(int timeout_ms);
    ZmqConfig& SetReceiveTimeout(int timeout_ms);
    ZmqConfig& SetConnectionTimeout(int timeout_ms);
    ZmqConfig& SetLinger(int linger_ms);
    ZmqConfig& SetHighWaterMark(int hwm);
    ZmqConfig& SetMaxBufferSize(size_t size);
    ZmqConfig& SetMaxRetries(int retries);
    ZmqConfig& SetRetryInterval(int interval_ms);
    ZmqConfig& SetAutoReconnect(bool enable);
    
    /**
     * @brief 设置套接字选项
     * @param option 选项名称
     * @param value 选项值
     */
    ZmqConfig& SetSocketOption(int option, int value);
    ZmqConfig& SetSocketOption(int option, const std::string& value);
    
    /**
     * @brief 获取套接字选项
     * @param option 选项名称
     * @return 选项值（如果存在）
     */
    bool GetSocketOptionInt(int option, int& value) const;
    bool GetSocketOptionString(int option, std::string& value) const;
    
    /**
     * @brief 验证配置的有效性
     * @throws ZmqConfigException 配置无效时
     */
    void Validate() const;
    
    /**
     * @brief 创建默认的客户端配置
     * @param endpoint 服务器端点
     * @return 客户端配置
     */
    static ZmqConfig CreateClientConfig(const std::string& endpoint);
    
    /**
     * @brief 创建默认的服务器配置
     * @param endpoint 绑定端点
     * @return 服务器配置
     */
    static ZmqConfig CreateServerConfig(const std::string& endpoint);
    
    /**
     * @brief 创建发布者配置
     * @param endpoint 绑定端点
     * @return 发布者配置
     */
    static ZmqConfig CreatePublisherConfig(const std::string& endpoint);
    
    /**
     * @brief 创建订阅者配置
     * @param endpoint 连接端点
     * @param topic 订阅主题（可选）
     * @return 订阅者配置
     */
    static ZmqConfig CreateSubscriberConfig(const std::string& endpoint, 
                                           const std::string& topic = "");

private:
    std::string endpoint_;                          // 连接端点
    SocketType socket_type_;                        // 套接字类型
    int send_timeout_ms_;                          // 发送超时（毫秒）
    int receive_timeout_ms_;                       // 接收超时（毫秒）
    int connection_timeout_ms_;                    // 连接超时（毫秒）
    int linger_ms_;                               // 关闭时等待时间
    int high_water_mark_;                         // 高水位标记
    size_t max_buffer_size_;                      // 最大缓冲区大小
    int max_retries_;                             // 最大重试次数
    int retry_interval_ms_;                       // 重试间隔
    bool auto_reconnect_;                         // 是否自动重连
    
    // 自定义套接字选项
    std::map<int, int> int_options_;
    std::map<int, std::string> string_options_;
};

} // namespace MQ

#endif // MQ_ZMQ_CONFIG_H
