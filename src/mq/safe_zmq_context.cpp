#include "safe_zmq_context.h"
#include "zmq_exception.h"
#include <chrono>
#include <thread>

namespace MQ {

// 静态成员初始化
std::weak_ptr<SafeZmqContext> SafeZmqContext::default_context_;
std::mutex SafeZmqContext::default_context_mutex_;

SafeZmqContext::SafeZmqContext() 
    : context_(nullptr), is_shutdown_(false), socket_count_(0) {
    Initialize(1);
}

SafeZmqContext::SafeZmqContext(int io_threads) 
    : context_(nullptr), is_shutdown_(false), socket_count_(0) {
    Initialize(io_threads);
}

SafeZmqContext::~SafeZmqContext() {
    Cleanup();
}

SafeZmqContext::SafeZmqContext(SafeZmqContext&& other) noexcept
    : context_(nullptr), is_shutdown_(false), socket_count_(0) {
    std::lock_guard<std::mutex> lock(other.mutex_);
    context_ = other.context_;
    is_shutdown_.store(other.is_shutdown_.load());
    socket_count_.store(other.socket_count_.load());
    
    other.context_ = nullptr;
    other.is_shutdown_.store(true);
    other.socket_count_.store(0);
}

SafeZmqContext& SafeZmqContext::operator=(SafeZmqContext&& other) noexcept {
    if (this != &other) {
        Cleanup();
        
        std::lock_guard<std::mutex> lock(other.mutex_);
        context_ = other.context_;
        is_shutdown_.store(other.is_shutdown_.load());
        socket_count_.store(other.socket_count_.load());
        
        other.context_ = nullptr;
        other.is_shutdown_.store(true);
        other.socket_count_.store(0);
    }
    return *this;
}

void* SafeZmqContext::GetContext() const {
    std::lock_guard<std::mutex> lock(mutex_);
    if (!IsValid()) {
        throw ZmqResourceException("ZMQ Context");
    }
    return context_;
}

bool SafeZmqContext::IsValid() const noexcept {
    return context_ != nullptr && !is_shutdown_.load();
}

void SafeZmqContext::SetOption(int option, int value) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (!IsValid()) {
        throw ZmqResourceException("ZMQ Context");
    }
    
    int result = zmq_ctx_set(context_, option, value);
    CheckZmqResult(result, "zmq_ctx_set");
}

int SafeZmqContext::GetOption(int option) const {
    std::lock_guard<std::mutex> lock(mutex_);
    if (!IsValid()) {
        throw ZmqResourceException("ZMQ Context");
    }
    
    int result = zmq_ctx_get(context_, option);
    if (result == -1) {
        CheckZmqResult(-1, "zmq_ctx_get");
    }
    return result;
}

bool SafeZmqContext::Shutdown(int timeout_ms) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (is_shutdown_.load()) {
        return true;
    }
    
    is_shutdown_.store(true);
    
    // 等待所有socket关闭
    auto start_time = std::chrono::steady_clock::now();
    while (socket_count_.load() > 0) {
        if (timeout_ms >= 0) {
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now() - start_time).count();
            if (elapsed >= timeout_ms) {
                return false;  // 超时
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    return true;
}

int SafeZmqContext::GetActiveSocketCount() const {
    return socket_count_.load();
}

std::shared_ptr<SafeZmqContext> SafeZmqContext::CreateShared(int io_threads) {
    return std::make_shared<SafeZmqContext>(io_threads);
}

std::shared_ptr<SafeZmqContext> SafeZmqContext::GetDefault() {
    std::lock_guard<std::mutex> lock(default_context_mutex_);
    
    auto context = default_context_.lock();
    if (!context) {
        context = std::make_shared<SafeZmqContext>(1);
        default_context_ = context;
    }
    
    return context;
}

void SafeZmqContext::Initialize(int io_threads) {
    context_ = zmq_ctx_new();
    CheckZmqPointer(context_, "ZMQ Context");
    
    try {
        // 设置I/O线程数
        if (io_threads > 0) {
            SetOption(ZMQ_IO_THREADS, io_threads);
        }
        
        // 设置最大socket数
        SetOption(ZMQ_MAX_SOCKETS, 1024);
        
    } catch (...) {
        if (context_) {
            zmq_ctx_destroy(context_);
            context_ = nullptr;
        }
        throw;
    }
}

void SafeZmqContext::Cleanup() {
    if (context_) {
        // 尝试优雅关闭
        Shutdown(5000);
        
        // 强制销毁context
        zmq_ctx_destroy(context_);
        context_ = nullptr;
    }
}

void SafeZmqContext::IncrementSocketCount() {
    socket_count_.fetch_add(1);
}

void SafeZmqContext::DecrementSocketCount() {
    socket_count_.fetch_sub(1);
}

// ZmqContextManager实现
ZmqContextManager& ZmqContextManager::GetInstance() {
    static ZmqContextManager instance;
    return instance;
}

ZmqContextManager::~ZmqContextManager() {
    ShutdownAll(5000);
}

std::shared_ptr<SafeZmqContext> ZmqContextManager::GetContext(const std::string& name, int io_threads) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = contexts_.find(name);
    if (it != contexts_.end() && it->second->IsValid()) {
        return it->second;
    }
    
    auto context = std::make_shared<SafeZmqContext>(io_threads);
    contexts_[name] = context;
    return context;
}

void ZmqContextManager::RemoveContext(const std::string& name) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = contexts_.find(name);
    if (it != contexts_.end()) {
        it->second->Shutdown(3000);
        contexts_.erase(it);
    }
}

void ZmqContextManager::ShutdownAll(int timeout_ms) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    for (auto& pair : contexts_) {
        pair.second->Shutdown(timeout_ms);
    }
    contexts_.clear();
}

size_t ZmqContextManager::GetContextCount() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return contexts_.size();
}

} // namespace MQ
