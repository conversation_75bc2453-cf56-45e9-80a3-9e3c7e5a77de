#ifndef MQ_ZMQ_EXCEPTION_H
#define MQ_ZMQ_EXCEPTION_H

#include <stdexcept>
#include <string>
#include <zmq.h>

namespace MQ {

/**
 * @brief ZeroMQ异常基类
 * 
 * 提供详细的错误信息和错误代码，支持ZeroMQ特定的错误处理
 */
class ZmqException : public std::runtime_error {
public:
    /**
     * @brief 构造函数
     * @param message 错误消息
     * @param error_code ZeroMQ错误代码（默认为当前errno）
     */
    explicit ZmqException(const std::string& message, int error_code = zmq_errno());
    
    /**
     * @brief 获取ZeroMQ错误代码
     * @return 错误代码
     */
    int GetErrorCode() const noexcept { return error_code_; }
    
    /**
     * @brief 获取ZeroMQ错误描述
     * @return 错误描述字符串
     */
    std::string GetZmqErrorString() const;
    
    /**
     * @brief 获取完整的错误信息（包含错误代码和描述）
     * @return 完整错误信息
     */
    std::string GetFullErrorMessage() const;

private:
    int error_code_;
};

/**
 * @brief 连接异常
 */
class ZmqConnectionException : public ZmqException {
public:
    explicit ZmqConnectionException(const std::string& endpoint, int error_code = zmq_errno());
};

/**
 * @brief 发送异常
 */
class ZmqSendException : public ZmqException {
public:
    explicit ZmqSendException(const std::string& message = "Failed to send message", 
                             int error_code = zmq_errno());
};

/**
 * @brief 接收异常
 */
class ZmqReceiveException : public ZmqException {
public:
    explicit ZmqReceiveException(const std::string& message = "Failed to receive message", 
                                int error_code = zmq_errno());
};

/**
 * @brief 超时异常
 */
class ZmqTimeoutException : public ZmqException {
public:
    explicit ZmqTimeoutException(const std::string& operation, int timeout_ms);
};

/**
 * @brief 配置异常
 */
class ZmqConfigException : public ZmqException {
public:
    explicit ZmqConfigException(const std::string& message);
};

/**
 * @brief 资源异常（如context或socket创建失败）
 */
class ZmqResourceException : public ZmqException {
public:
    explicit ZmqResourceException(const std::string& resource_type, 
                                 int error_code = zmq_errno());
};

/**
 * @brief 工具函数：检查ZeroMQ返回值并在错误时抛出异常
 * @param result ZeroMQ函数返回值
 * @param operation 操作描述
 * @throws ZmqException 当result为-1时
 */
void CheckZmqResult(int result, const std::string& operation);

/**
 * @brief 工具函数：检查指针并在为空时抛出异常
 * @param ptr 要检查的指针
 * @param resource_type 资源类型描述
 * @throws ZmqResourceException 当ptr为nullptr时
 */
void CheckZmqPointer(void* ptr, const std::string& resource_type);

} // namespace MQ

#endif // MQ_ZMQ_EXCEPTION_H
