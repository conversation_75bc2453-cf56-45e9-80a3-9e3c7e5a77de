project(mq)

find_package(Qt5 COMPONENTS Core REQUIRED)
find_package(ZeroMQ CONFIG REQUIRED)

# 创建安全ZeroMQ库
add_library(${PROJECT_NAME} STATIC
    zmq_exception.h
    zmq_exception.cpp
    zmq_config.h
    zmq_config.cpp
    safe_zmq_context.h
    safe_zmq_context.cpp
    safe_zmq_socket.h
    safe_zmq_socket.cpp
    zmq_client.h
    zmq_client.cpp
)

target_link_libraries(${PROJECT_NAME} PRIVATE
    Qt5::Core
    libzmq libzmq-static
)

target_include_directories(${PROJECT_NAME} PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# 设置C++标准
target_compile_features(${PROJECT_NAME} PRIVATE cxx_std_14)

# 如果是Debug模式，启用更多警告
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    if(MSVC)
        target_compile_options(${PROJECT_NAME} PRIVATE /W4)
    else()
        target_compile_options(${PROJECT_NAME} PRIVATE -Wall -Wextra -Wpedantic)
    endif()
endif()
