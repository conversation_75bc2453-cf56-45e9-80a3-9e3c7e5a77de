project(mq)

find_package(Qt5 COMPONENTS Core REQUIRED)
find_package(ZeroMQ CONFIG REQUIRED)

# 创建简洁的安全ZeroMQ库
add_library(${PROJECT_NAME} STATIC
    safe_zmq.h
    safe_zmq.cpp
)

target_link_libraries(${PROJECT_NAME} PRIVATE
    Qt5::Core
    libzmq libzmq-static
)

target_include_directories(${PROJECT_NAME} PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# 设置C++标准
target_compile_features(${PROJECT_NAME} PRIVATE cxx_std_14)
