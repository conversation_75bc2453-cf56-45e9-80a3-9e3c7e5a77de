#include "zmq_client.h"
#include "zmq_exception.h"
#include <QJsonDocument>
#include <QJsonParseError>
#include <thread>
#include <future>

namespace MQ {

ZmqClient::ZmqClient(const QString& endpoint, SocketType socket_type)
    : config_(ZmqConfig::CreateClientConfig(QStringToStdString(endpoint))) {
    config_.SetSocketType(socket_type);
    Initialize();
}

ZmqClient::ZmqClient(const ZmqConfig& config)
    : config_(config) {
    Initialize();
}

ZmqClient::~ZmqClient() {
    if (socket_) {
        socket_->Close();
    }
}

ZmqClient::ZmqClient(ZmqClient&& other) noexcept
    : context_(std::move(other.context_))
    , socket_(std::move(other.socket_))
    , config_(std::move(other.config_))
    , stats_(std::move(other.stats_)) {
}

ZmqClient& ZmqClient::operator=(ZmqClient&& other) noexcept {
    if (this != &other) {
        if (socket_) {
            socket_->Close();
        }
        
        context_ = std::move(other.context_);
        socket_ = std::move(other.socket_);
        config_ = std::move(other.config_);
        stats_ = std::move(other.stats_);
    }
    return *this;
}

void ZmqClient::Connect() {
    if (!socket_) {
        throw ZmqException("Socket not initialized");
    }
    
    try {
        socket_->Connect();
        UpdateStats();
    } catch (const ZmqException& e) {
        UpdateStats(0, 0, false, false);
        stats_.connection_errors++;
        throw;
    }
}

void ZmqClient::Disconnect() {
    if (socket_) {
        socket_->Disconnect();
    }
}

bool ZmqClient::IsConnected() const {
    return socket_ && socket_->IsConnected();
}

void ZmqClient::SendString(const QString& message) {
    if (!socket_) {
        throw ZmqSendException("Socket not initialized");
    }
    
    try {
        std::string std_message = QStringToStdString(message);
        size_t bytes_sent = socket_->Send(std_message);
        UpdateStats(bytes_sent, 0);
    } catch (const ZmqException& e) {
        UpdateStats(0, 0, true, false);
        throw;
    }
}

QString ZmqClient::ReceiveString() {
    if (!socket_) {
        throw ZmqReceiveException("Socket not initialized");
    }
    
    try {
        std::string received = socket_->ReceiveString();
        UpdateStats(0, received.size());
        return StdStringToQString(received);
    } catch (const ZmqException& e) {
        UpdateStats(0, 0, false, true);
        throw;
    }
}

void ZmqClient::SendJson(const QJsonObject& json, bool compact) {
    QJsonDocument doc(json);
    QByteArray data = compact ? doc.toJson(QJsonDocument::Compact) : doc.toJson();
    QString json_string = QString::fromUtf8(data);
    SendString(json_string);
}

QJsonObject ZmqClient::ReceiveJson() {
    QString json_string = ReceiveString();
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(json_string.toUtf8(), &error);
    
    if (error.error != QJsonParseError::NoError) {
        throw ZmqReceiveException("Failed to parse JSON: " + error.errorString().toStdString());
    }
    
    if (!doc.isObject()) {
        throw ZmqReceiveException("Received JSON is not an object");
    }
    
    return doc.object();
}

QString ZmqClient::SendRequest(const QString& request) {
    SendString(request);
    return ReceiveString();
}

QJsonObject ZmqClient::SendRequest(const QJsonObject& request) {
    SendJson(request);
    return ReceiveJson();
}

void ZmqClient::SendRequestAsync(const QString& request,
                                std::function<void(const QString&)> callback,
                                int timeout_ms) {
    std::thread([this, request, callback, timeout_ms]() {
        try {
            // 设置临时超时
            auto original_timeout = config_.GetReceiveTimeoutMs();
            socket_->SetOption(ZMQ_RCVTIMEO, timeout_ms);
            
            QString response = SendRequest(request);
            
            // 恢复原始超时
            socket_->SetOption(ZMQ_RCVTIMEO, original_timeout);
            
            callback(response);
        } catch (const ZmqException& e) {
            callback(QString("Error: %1").arg(e.what()));
        }
    }).detach();
}

void ZmqClient::SendRequestAsync(const QJsonObject& request,
                                std::function<void(const QJsonObject&)> callback,
                                int timeout_ms) {
    std::thread([this, request, callback, timeout_ms]() {
        try {
            // 设置临时超时
            auto original_timeout = config_.GetReceiveTimeoutMs();
            socket_->SetOption(ZMQ_RCVTIMEO, timeout_ms);
            
            QJsonObject response = SendRequest(request);
            
            // 恢复原始超时
            socket_->SetOption(ZMQ_RCVTIMEO, original_timeout);
            
            callback(response);
        } catch (const ZmqException& e) {
            QJsonObject error_response;
            error_response["error"] = QString("ZMQ Error: %1").arg(e.what());
            callback(error_response);
        }
    }).detach();
}

void ZmqClient::SetTimeout(int send_timeout_ms, int receive_timeout_ms) {
    config_.SetSendTimeout(send_timeout_ms).SetReceiveTimeout(receive_timeout_ms);
    
    if (socket_) {
        socket_->SetOption(ZMQ_SNDTIMEO, send_timeout_ms);
        socket_->SetOption(ZMQ_RCVTIMEO, receive_timeout_ms);
    }
}

void ZmqClient::SetRetryPolicy(int max_retries, int retry_interval_ms) {
    config_.SetMaxRetries(max_retries).SetRetryInterval(retry_interval_ms);
}

void ZmqClient::SetAutoReconnect(bool enable) {
    config_.SetAutoReconnect(enable);
}

const ZmqConfig& ZmqClient::GetConfig() const {
    return config_;
}

ZmqClient::ConnectionStats ZmqClient::GetStats() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

void ZmqClient::ResetStats() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_ = ConnectionStats{};
}

std::unique_ptr<ZmqClient> ZmqClient::CreateLLMRuleClient(const QString& endpoint) {
    auto config = ZmqConfig::CreateClientConfig(endpoint.toStdString())
        .SetSendTimeout(5000)
        .SetReceiveTimeout(30000)  // LLM响应可能较慢
        .SetMaxRetries(3)
        .SetAutoReconnect(true);
    
    return std::make_unique<ZmqClient>(config);
}

void ZmqClient::Initialize() {
    context_ = SafeZmqContext::GetDefault();
    socket_ = std::make_unique<SafeZmqSocket>(context_, config_);
    stats_ = ConnectionStats{};
    stats_.last_activity = std::chrono::steady_clock::now();
}

void ZmqClient::UpdateStats(size_t bytes_sent, size_t bytes_received,
                           bool send_error, bool receive_error) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    if (bytes_sent > 0) {
        stats_.messages_sent++;
        stats_.bytes_sent += bytes_sent;
    }
    
    if (bytes_received > 0) {
        stats_.messages_received++;
        stats_.bytes_received += bytes_received;
    }
    
    if (send_error) {
        stats_.send_errors++;
    }
    
    if (receive_error) {
        stats_.receive_errors++;
    }
    
    stats_.last_activity = std::chrono::steady_clock::now();
}

std::string ZmqClient::QStringToStdString(const QString& qstr) const {
    return qstr.toUtf8().toStdString();
}

QString ZmqClient::StdStringToQString(const std::string& str) const {
    return QString::fromUtf8(str.c_str());
}

// ZmqServer实现
ZmqServer::ZmqServer(const QString& endpoint)
    : config_(ZmqConfig::CreateServerConfig(endpoint.toStdString()))
    , is_running_(false) {
    Initialize();
}

ZmqServer::ZmqServer(const ZmqConfig& config)
    : config_(config)
    , is_running_(false) {
    Initialize();
}

ZmqServer::~ZmqServer() {
    Stop();
}

void ZmqServer::Start() {
    if (!socket_) {
        throw ZmqException("Socket not initialized");
    }
    
    socket_->Bind();
    is_running_.store(true);
}

void ZmqServer::Stop() {
    is_running_.store(false);
    if (socket_) {
        socket_->Close();
    }
}

bool ZmqServer::IsRunning() const {
    return is_running_.load();
}

QString ZmqServer::ReceiveRequest() {
    if (!socket_) {
        throw ZmqReceiveException("Socket not initialized");
    }
    
    std::string received = socket_->ReceiveString();
    return QString::fromUtf8(received.c_str());
}

void ZmqServer::SendResponse(const QString& response) {
    if (!socket_) {
        throw ZmqSendException("Socket not initialized");
    }
    
    std::string std_response = response.toUtf8().toStdString();
    socket_->Send(std_response);
}

QJsonObject ZmqServer::ReceiveJsonRequest() {
    QString json_string = ReceiveRequest();
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(json_string.toUtf8(), &error);
    
    if (error.error != QJsonParseError::NoError) {
        throw ZmqReceiveException("Failed to parse JSON: " + error.errorString().toStdString());
    }
    
    return doc.object();
}

void ZmqServer::SendJsonResponse(const QJsonObject& response) {
    QJsonDocument doc(response);
    QString json_string = QString::fromUtf8(doc.toJson(QJsonDocument::Compact));
    SendResponse(json_string);
}

void ZmqServer::Initialize() {
    context_ = SafeZmqContext::GetDefault();
    socket_ = std::make_unique<SafeZmqSocket>(context_, config_);
}

} // namespace MQ
