#ifndef MQ_SAFE_ZMQ_SOCKET_H
#define MQ_SAFE_ZMQ_SOCKET_H

#include "safe_zmq_context.h"
#include "zmq_config.h"
#include <memory>
#include <vector>
#include <mutex>
#include <atomic>
#include <chrono>

namespace MQ {

/**
 * @brief 安全的ZeroMQ Socket封装类
 * 
 * 提供线程安全的socket操作，支持超时控制、动态缓冲区、自动重连等功能
 */
class SafeZmqSocket {
public:
    /**
     * @brief 构造函数
     * @param context 共享的ZMQ context
     * @param config socket配置
     */
    SafeZmqSocket(std::shared_ptr<SafeZmqContext> context, const ZmqConfig& config);
    
    /**
     * @brief 析构函数
     * 自动关闭socket并清理资源
     */
    ~SafeZmqSocket();
    
    // 禁用拷贝构造和赋值
    SafeZmqSocket(const SafeZmqSocket&) = delete;
    SafeZmqSocket& operator=(const SafeZmqSocket&) = delete;
    
    // 支持移动语义
    SafeZmqSocket(SafeZmqSocket&& other) noexcept;
    SafeZmqSocket& operator=(SafeZmqSocket&& other) noexcept;
    
    /**
     * @brief 连接到端点
     * @throws ZmqConnectionException 连接失败时
     */
    void Connect();
    
    /**
     * @brief 绑定到端点
     * @throws ZmqConnectionException 绑定失败时
     */
    void Bind();
    
    /**
     * @brief 断开连接
     */
    void Disconnect();
    
    /**
     * @brief 关闭socket
     */
    void Close();
    
    /**
     * @brief 发送数据
     * @param data 要发送的数据
     * @param size 数据大小
     * @param flags 发送标志
     * @return 发送的字节数
     * @throws ZmqSendException 发送失败时
     */
    size_t Send(const void* data, size_t size, int flags = 0);
    
    /**
     * @brief 发送字符串
     * @param message 要发送的字符串
     * @param flags 发送标志
     * @return 发送的字节数
     */
    size_t Send(const std::string& message, int flags = 0);
    
    /**
     * @brief 发送多部分消息
     * @param parts 消息部分列表
     * @throws ZmqSendException 发送失败时
     */
    void SendMultipart(const std::vector<std::string>& parts);
    
    /**
     * @brief 接收数据
     * @param buffer 接收缓冲区
     * @param max_size 最大接收大小
     * @param flags 接收标志
     * @return 接收的字节数
     * @throws ZmqReceiveException 接收失败时
     */
    size_t Receive(void* buffer, size_t max_size, int flags = 0);
    
    /**
     * @brief 接收字符串
     * @param flags 接收标志
     * @return 接收到的字符串
     */
    std::string ReceiveString(int flags = 0);
    
    /**
     * @brief 接收到动态缓冲区
     * @param flags 接收标志
     * @return 接收到的数据
     */
    std::vector<char> ReceiveBuffer(int flags = 0);
    
    /**
     * @brief 接收多部分消息
     * @return 消息部分列表
     */
    std::vector<std::string> ReceiveMultipart();
    
    /**
     * @brief 检查是否有更多消息部分
     * @return true如果有更多部分
     */
    bool HasMore() const;
    
    /**
     * @brief 设置socket选项
     * @param option 选项名称
     * @param value 选项值
     */
    void SetOption(int option, int value);
    void SetOption(int option, const std::string& value);
    
    /**
     * @brief 获取socket选项
     * @param option 选项名称
     * @return 选项值
     */
    int GetOptionInt(int option) const;
    std::string GetOptionString(int option) const;
    
    /**
     * @brief 检查socket是否有效
     * @return true如果socket有效
     */
    bool IsValid() const noexcept;
    
    /**
     * @brief 检查是否已连接
     * @return true如果已连接
     */
    bool IsConnected() const noexcept;
    
    /**
     * @brief 获取配置
     * @return socket配置
     */
    const ZmqConfig& GetConfig() const { return config_; }
    
    /**
     * @brief 重新连接
     * @throws ZmqConnectionException 重连失败时
     */
    void Reconnect();
    
    /**
     * @brief 等待socket可读
     * @param timeout_ms 超时时间（毫秒）
     * @return true如果可读，false如果超时
     */
    bool WaitForRead(int timeout_ms) const;
    
    /**
     * @brief 等待socket可写
     * @param timeout_ms 超时时间（毫秒）
     * @return true如果可写，false如果超时
     */
    bool WaitForWrite(int timeout_ms) const;

private:
    std::shared_ptr<SafeZmqContext> context_;  // 共享的context
    void* socket_;                             // ZMQ socket指针
    ZmqConfig config_;                         // socket配置
    mutable std::mutex mutex_;                 // 保护并发访问的互斥锁
    std::atomic<bool> is_connected_;           // 连接状态
    std::atomic<bool> is_closed_;              // 关闭状态
    std::vector<char> receive_buffer_;         // 接收缓冲区
    
    /**
     * @brief 初始化socket
     */
    void Initialize();
    
    /**
     * @brief 应用配置选项
     */
    void ApplyConfig();
    
    /**
     * @brief 清理资源
     */
    void Cleanup();
    
    /**
     * @brief 带重试的操作
     * @param operation 操作函数
     * @param operation_name 操作名称
     * @return 操作结果
     */
    template<typename Func>
    auto WithRetry(Func&& operation, const std::string& operation_name) -> decltype(operation());
    
    /**
     * @brief 检查并调整缓冲区大小
     * @param required_size 需要的大小
     */
    void EnsureBufferSize(size_t required_size);
    
    /**
     * @brief 轮询socket状态
     * @param events 要检查的事件
     * @param timeout_ms 超时时间
     * @return 发生的事件
     */
    int Poll(int events, int timeout_ms) const;
};

} // namespace MQ

#endif // MQ_SAFE_ZMQ_SOCKET_H
