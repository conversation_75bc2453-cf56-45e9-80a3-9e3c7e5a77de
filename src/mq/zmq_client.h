#ifndef MQ_ZMQ_CLIENT_H
#define MQ_ZMQ_CLIENT_H

#include "safe_zmq_socket.h"
#include "zmq_config.h"
#include <QJsonObject>
#include <QJsonDocument>
#include <QString>
#include <QStringList>
#include <memory>
#include <functional>

namespace MQ {

/**
 * @brief 高级ZeroMQ客户端类
 * 
 * 提供简化的API接口，支持JSON和字符串操作，专门用于替代现有的LLMRuleAgent
 */
class ZmqClient {
public:
    /**
     * @brief 构造函数
     * @param endpoint 服务器端点
     * @param socket_type 套接字类型（默认为Request）
     */
    explicit ZmqClient(const QString& endpoint, 
                      SocketType socket_type = SocketType::Request);
    
    /**
     * @brief 构造函数
     * @param config 详细配置
     */
    explicit ZmqClient(const ZmqConfig& config);
    
    /**
     * @brief 析构函数
     */
    ~ZmqClient();
    
    // 禁用拷贝构造和赋值
    ZmqClient(const ZmqClient&) = delete;
    ZmqClient& operator=(const ZmqClient&) = delete;
    
    // 支持移动语义
    ZmqClient(ZmqClient&& other) noexcept;
    ZmqClient& operator=(ZmqClient&& other) noexcept;
    
    /**
     * @brief 连接到服务器
     * @throws ZmqConnectionException 连接失败时
     */
    void Connect();
    
    /**
     * @brief 断开连接
     */
    void Disconnect();
    
    /**
     * @brief 检查是否已连接
     * @return true如果已连接
     */
    bool IsConnected() const;
    
    /**
     * @brief 发送字符串消息
     * @param message 要发送的消息
     * @throws ZmqSendException 发送失败时
     */
    void SendString(const QString& message);
    
    /**
     * @brief 接收字符串消息
     * @return 接收到的消息
     * @throws ZmqReceiveException 接收失败时
     */
    QString ReceiveString();
    
    /**
     * @brief 发送JSON对象
     * @param json 要发送的JSON对象
     * @param compact 是否使用紧凑格式（默认true）
     * @throws ZmqSendException 发送失败时
     */
    void SendJson(const QJsonObject& json, bool compact = true);
    
    /**
     * @brief 接收JSON对象
     * @return 接收到的JSON对象
     * @throws ZmqReceiveException 接收失败时
     */
    QJsonObject ReceiveJson();
    
    /**
     * @brief 发送请求并接收响应（字符串版本）
     * @param request 请求消息
     * @return 响应消息
     * @throws ZmqException 操作失败时
     */
    QString SendRequest(const QString& request);
    
    /**
     * @brief 发送请求并接收响应（JSON版本）
     * @param request 请求JSON对象
     * @return 响应JSON对象
     * @throws ZmqException 操作失败时
     */
    QJsonObject SendRequest(const QJsonObject& request);
    
    /**
     * @brief 异步发送请求并接收响应
     * @param request 请求消息
     * @param callback 响应回调函数
     * @param timeout_ms 超时时间（毫秒）
     */
    void SendRequestAsync(const QString& request, 
                         std::function<void(const QString&)> callback,
                         int timeout_ms = 5000);
    
    /**
     * @brief 异步发送请求并接收响应（JSON版本）
     * @param request 请求JSON对象
     * @param callback 响应回调函数
     * @param timeout_ms 超时时间（毫秒）
     */
    void SendRequestAsync(const QJsonObject& request,
                         std::function<void(const QJsonObject&)> callback,
                         int timeout_ms = 5000);
    
    /**
     * @brief 设置超时时间
     * @param send_timeout_ms 发送超时（毫秒）
     * @param receive_timeout_ms 接收超时（毫秒）
     */
    void SetTimeout(int send_timeout_ms, int receive_timeout_ms);
    
    /**
     * @brief 设置重试参数
     * @param max_retries 最大重试次数
     * @param retry_interval_ms 重试间隔（毫秒）
     */
    void SetRetryPolicy(int max_retries, int retry_interval_ms);
    
    /**
     * @brief 启用/禁用自动重连
     * @param enable 是否启用
     */
    void SetAutoReconnect(bool enable);
    
    /**
     * @brief 获取配置
     * @return 当前配置
     */
    const ZmqConfig& GetConfig() const;
    
    /**
     * @brief 获取连接统计信息
     */
    struct ConnectionStats {
        size_t messages_sent = 0;
        size_t messages_received = 0;
        size_t bytes_sent = 0;
        size_t bytes_received = 0;
        size_t connection_errors = 0;
        size_t send_errors = 0;
        size_t receive_errors = 0;
        std::chrono::steady_clock::time_point last_activity;
    };
    
    /**
     * @brief 获取连接统计信息
     * @return 统计信息
     */
    ConnectionStats GetStats() const;
    
    /**
     * @brief 重置统计信息
     */
    void ResetStats();
    
    /**
     * @brief 创建用于LLM规则代理的客户端
     * @param endpoint 服务器端点
     * @return 配置好的客户端实例
     */
    static std::unique_ptr<ZmqClient> CreateLLMRuleClient(const QString& endpoint);

private:
    std::shared_ptr<SafeZmqContext> context_;
    std::unique_ptr<SafeZmqSocket> socket_;
    ZmqConfig config_;
    mutable std::mutex stats_mutex_;
    ConnectionStats stats_;
    
    /**
     * @brief 初始化客户端
     */
    void Initialize();
    
    /**
     * @brief 更新统计信息
     * @param bytes_sent 发送字节数
     * @param bytes_received 接收字节数
     * @param send_error 是否发送错误
     * @param receive_error 是否接收错误
     */
    void UpdateStats(size_t bytes_sent = 0, size_t bytes_received = 0,
                    bool send_error = false, bool receive_error = false);
    
    /**
     * @brief 将QString转换为std::string
     * @param qstr Qt字符串
     * @return 标准字符串
     */
    std::string QStringToStdString(const QString& qstr) const;
    
    /**
     * @brief 将std::string转换为QString
     * @param str 标准字符串
     * @return Qt字符串
     */
    QString StdStringToQString(const std::string& str) const;
};

/**
 * @brief ZeroMQ服务器类
 * 
 * 提供简化的服务器端API
 */
class ZmqServer {
public:
    /**
     * @brief 构造函数
     * @param endpoint 绑定端点
     */
    explicit ZmqServer(const QString& endpoint);
    
    /**
     * @brief 构造函数
     * @param config 详细配置
     */
    explicit ZmqServer(const ZmqConfig& config);
    
    /**
     * @brief 析构函数
     */
    ~ZmqServer();
    
    // 禁用拷贝构造和赋值
    ZmqServer(const ZmqServer&) = delete;
    ZmqServer& operator=(const ZmqServer&) = delete;
    
    /**
     * @brief 启动服务器
     * @throws ZmqConnectionException 启动失败时
     */
    void Start();
    
    /**
     * @brief 停止服务器
     */
    void Stop();
    
    /**
     * @brief 检查服务器是否运行
     * @return true如果正在运行
     */
    bool IsRunning() const;
    
    /**
     * @brief 接收请求
     * @return 请求消息
     */
    QString ReceiveRequest();
    
    /**
     * @brief 发送响应
     * @param response 响应消息
     */
    void SendResponse(const QString& response);
    
    /**
     * @brief 接收JSON请求
     * @return 请求JSON对象
     */
    QJsonObject ReceiveJsonRequest();
    
    /**
     * @brief 发送JSON响应
     * @param response 响应JSON对象
     */
    void SendJsonResponse(const QJsonObject& response);

private:
    std::shared_ptr<SafeZmqContext> context_;
    std::unique_ptr<SafeZmqSocket> socket_;
    ZmqConfig config_;
    std::atomic<bool> is_running_;
    
    void Initialize();
};

} // namespace MQ

#endif // MQ_ZMQ_CLIENT_H
