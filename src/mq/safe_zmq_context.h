#ifndef MQ_SAFE_ZMQ_CONTEXT_H
#define MQ_SAFE_ZMQ_CONTEXT_H

#include <zmq.h>
#include <map>
#include <memory>
#include <mutex>
#include <string>

namespace MQ {

/**
 * @brief 安全的ZeroMQ Context管理类
 *
 * 使用RAII模式管理ZeroMQ context的生命周期，确保资源正确释放
 * 支持线程安全的操作和引用计数
 */
class SafeZmqContext {
 public:
  /**
     * @brief 默认构造函数
     * 创建新的ZeroMQ context
     */
  SafeZmqContext();

  /**
     * @brief 构造函数
     * @param io_threads I/O线程数量
     */
  explicit SafeZmqContext(int io_threads);

  /**
     * @brief 析构函数
     * 自动销毁context并等待所有socket关闭
     */
  ~SafeZmqContext();

  // 禁用拷贝构造和赋值
  SafeZmqContext(const SafeZmqContext&) = delete;
  SafeZmqContext& operator=(const SafeZmqContext&) = delete;

  // 支持移动语义
  SafeZmqContext(SafeZmqContext&& other) noexcept;
  SafeZmqContext& operator=(SafeZmqContext&& other) noexcept;

  /**
     * @brief 获取原始context指针
     * @return ZeroMQ context指针
     * @throws ZmqResourceException 如果context无效
     */
  void* GetContext() const;

  /**
     * @brief 检查context是否有效
     * @return true如果context有效
     */
  bool IsValid() const noexcept;

  /**
     * @brief 设置context选项
     * @param option 选项名称
     * @param value 选项值
     * @throws ZmqException 设置失败时
     */
  void SetOption(int option, int value);

  /**
     * @brief 获取context选项
     * @param option 选项名称
     * @return 选项值
     * @throws ZmqException 获取失败时
     */
  int GetOption(int option) const;

  /**
     * @brief 优雅关闭context
     * 等待所有socket关闭后再销毁context
     * @param timeout_ms 超时时间（毫秒），-1表示无限等待
     * @return true如果成功关闭，false如果超时
     */
  bool Shutdown(int timeout_ms = 5000);

  /**
     * @brief 获取活跃socket数量
     * @return socket数量
     */
  int GetActiveSocketCount() const;

  /**
     * @brief 创建共享的context实例
     * 使用单例模式，多个对象可以共享同一个context
     * @param io_threads I/O线程数量
     * @return 共享的context实例
     */
  static std::shared_ptr<SafeZmqContext> CreateShared(int io_threads = 1);

  /**
     * @brief 获取默认的共享context
     * @return 默认共享context
     */
  static std::shared_ptr<SafeZmqContext> GetDefault();

 private:
  void* context_;                  // ZeroMQ context指针
  mutable std::mutex mutex_;       // 保护并发访问的互斥锁
  std::atomic<bool> is_shutdown_;  // 是否已关闭
  std::atomic<int> socket_count_;  // 活跃socket计数

  /**
     * @brief 初始化context
     * @param io_threads I/O线程数量
     */
  void Initialize(int io_threads);

  /**
     * @brief 清理资源
     */
  void Cleanup();

  // 友元类，允许SafeZmqSocket访问私有成员
  friend class SafeZmqSocket;

  /**
     * @brief 增加socket计数（由SafeZmqSocket调用）
     */
  void IncrementSocketCount();

  /**
     * @brief 减少socket计数（由SafeZmqSocket调用）
     */
  void DecrementSocketCount();

  // 静态成员
  static std::weak_ptr<SafeZmqContext> default_context_;
  static std::mutex default_context_mutex_;
};

/**
 * @brief Context管理器类
 * 提供更高级的context管理功能
 */
class ZmqContextManager {
 public:
  /**
     * @brief 获取单例实例
     */
  static ZmqContextManager& GetInstance();

  /**
     * @brief 获取或创建命名context
     * @param name context名称
     * @param io_threads I/O线程数量
     * @return 共享的context实例
     */
  std::shared_ptr<SafeZmqContext> GetContext(const std::string& name,
                                             int io_threads = 1);

  /**
     * @brief 移除命名context
     * @param name context名称
     */
  void RemoveContext(const std::string& name);

  /**
     * @brief 关闭所有context
     * @param timeout_ms 超时时间
     */
  void ShutdownAll(int timeout_ms = 5000);

  /**
     * @brief 获取context数量
     */
  size_t GetContextCount() const;

 private:
  ZmqContextManager() = default;
  ~ZmqContextManager();

  std::map<std::string, std::shared_ptr<SafeZmqContext>> contexts_;
  mutable std::mutex mutex_;
};

}  // namespace MQ

#endif  // MQ_SAFE_ZMQ_CONTEXT_H
