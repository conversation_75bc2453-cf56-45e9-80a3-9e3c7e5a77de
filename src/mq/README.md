# 安全的ZeroMQ封装库

这是一个简洁但安全的ZeroMQ封装库，专门用于替代项目中现有的不安全的ZeroMQ使用方式。

## 主要特性

### 🛡️ 安全性改进
- **RAII资源管理**：自动管理ZMQ context和socket，防止资源泄漏
- **动态缓冲区**：避免固定缓冲区溢出，自动扩展至最大10MB
- **异常安全**：使用异常而非返回码进行错误处理
- **超时控制**：可配置的发送/接收超时，防止无限阻塞

### 🚀 易用性
- **简洁API**：只需几行代码即可完成ZMQ通信
- **Qt集成**：原生支持QString和QJsonObject
- **工厂函数**：针对特定用途的预配置客户端

## 快速开始

### 基本用法

```cpp
#include "safe_zmq.h"

// 创建客户端
auto client = MQ::CreateLLMClient("tcp://10.0.25.110:5555");

// 发送字符串请求
QString response = client->SendRequest("Hello, Server!");

// 发送JSON请求
QJsonObject request;
request["task"] = "get_near_rule";
request["data"] = "some data";

QJsonObject json_response = client->SendRequest(request);
```

### 替代现有的LLMRuleAgent

**旧代码（不安全）：**
```cpp
// 在构造函数中
context_ = zmq_ctx_new();
socket_ = zmq_socket(context_, ZMQ_REQ);
zmq_connect(socket_, "tcp://10.0.25.110:5555");

// 发送消息
auto rc = zmq_send(socket_, buf.toStdString().c_str(), buf.size(), 0);

// 接收消息
constexpr int buf_size = 4096;  // 固定缓冲区！
char buffer[buf_size];
auto rc = zmq_recv(socket_, buffer, buf_size, 0);

// 手动清理
if (socket_) {
    zmq_close(socket_);
}
if (context_) {
    zmq_ctx_destroy(context_);
}
```

**新代码（安全）：**
```cpp
// 创建客户端（自动管理资源）
auto client = MQ::CreateLLMClient("tcp://10.0.25.110:5555");

// 发送和接收（异常安全，动态缓冲区）
try {
    QJsonObject response = client->SendRequest(request);
    // 处理响应
} catch (const MQ::ZmqException& e) {
    // 处理错误
    qDebug() << "ZMQ Error:" << e.what();
}
// 自动清理，无需手动管理
```

## API参考

### SafeZmqClient类

#### 构造函数
```cpp
SafeZmqClient(const QString& endpoint, int timeout_ms = 5000);
```

#### 主要方法
```cpp
// 字符串通信
void SendString(const QString& message);
QString ReceiveString();
QString SendRequest(const QString& request);

// JSON通信
void SendJson(const QJsonObject& json);
QJsonObject ReceiveJson();
QJsonObject SendRequest(const QJsonObject& request);

// 配置和控制
void SetTimeout(int timeout_ms);
void Reconnect();
bool IsConnected() const;
```

#### 工厂函数
```cpp
// 创建用于LLM通信的客户端（30秒超时）
std::unique_ptr<SafeZmqClient> CreateLLMClient(const QString& endpoint);
```

## 错误处理

所有ZMQ操作都可能抛出`MQ::ZmqException`异常：

```cpp
try {
    auto client = MQ::CreateLLMClient("tcp://server:5555");
    QString response = client->SendRequest("test");
} catch (const MQ::ZmqException& e) {
    std::cerr << "ZMQ Error: " << e.what() << std::endl;
    // 处理错误：重试、记录日志、用户提示等
}
```

## 性能特性

- **内存效率**：动态缓冲区按需分配，最大限制10MB
- **连接复用**：单个客户端可以发送多个请求
- **超时控制**：避免无限等待，提高响应性
- **异常开销**：正常情况下无异常开销

## 迁移指南

### 1. 更新包含文件
```cpp
// 旧
#include "llm_rule_agent.h"

// 新
#include "safe_zmq.h"
```

### 2. 替换类实例化
```cpp
// 旧
Agent::LLMRuleAgent agent;

// 新
auto client = MQ::CreateLLMClient("tcp://10.0.25.110:5555");
```

### 3. 更新错误处理
```cpp
// 旧
QJsonObject result = agent.GetNearRule(comp_list);
if (result.isEmpty()) {
    // 处理错误
}

// 新
try {
    QJsonObject result = client->SendRequest(request);
    // 处理成功结果
} catch (const MQ::ZmqException& e) {
    // 处理错误
}
```

## 编译配置

在CMakeLists.txt中添加：

```cmake
# 添加mq库
add_subdirectory(src/mq)

# 链接到你的目标
target_link_libraries(your_target PRIVATE mq)
```

## 测试

运行测试程序：
```bash
# 编译测试（如果启用了BUILD_TESTING）
cmake -DBUILD_TESTING=ON ..
make test_safe_zmq

# 运行测试
./test_safe_zmq
```

## 故障排除

### 常见问题

1. **连接超时**
   ```cpp
   client->SetTimeout(30000);  // 增加超时时间
   ```

2. **大消息处理**
   - 库自动处理最大10MB的消息
   - 超过限制会抛出异常

3. **服务器不可用**
   ```cpp
   try {
       client->Reconnect();
   } catch (const MQ::ZmqException& e) {
       // 服务器仍然不可用
   }
   ```

## 安全性保证

✅ **缓冲区溢出防护**：动态缓冲区，自动扩展  
✅ **资源泄漏防护**：RAII自动管理  
✅ **超时防护**：可配置超时，防止无限阻塞  
✅ **异常安全**：强异常安全保证  
✅ **内存限制**：最大10MB缓冲区，防止内存耗尽  

这个封装库解决了原有代码中的所有已知安全问题，同时保持了简洁易用的API。
