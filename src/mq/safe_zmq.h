#ifndef MQ_SAFE_ZMQ_H
#define MQ_SAFE_ZMQ_H

#include <zmq.h>
#include <QString>
#include <QJsonObject>
#include <QJsonDocument>
#include <memory>
#include <vector>
#include <stdexcept>

namespace MQ {

/**
 * @brief 简洁的ZeroMQ异常类
 */
class ZmqException : public std::runtime_error {
public:
    explicit ZmqException(const std::string& message) : std::runtime_error(message) {}
};

/**
 * @brief 安全的ZeroMQ客户端封装类
 * 
 * 解决现有LLMRuleAgent中的安全问题：
 * - 自动资源管理（RAII）
 * - 动态缓冲区，避免缓冲区溢出
 * - 异常安全的错误处理
 * - 超时控制
 * - 简洁的API接口
 */
class SafeZmqClient {
public:
    /**
     * @brief 构造函数
     * @param endpoint 服务器端点（如 "tcp://10.0.25.110:5555"）
     * @param timeout_ms 超时时间（毫秒），默认5秒
     */
    explicit SafeZmqClient(const QString& endpoint, int timeout_ms = 5000);
    
    /**
     * @brief 析构函数，自动清理资源
     */
    ~SafeZmqClient();
    
    // 禁用拷贝构造和赋值
    SafeZmqClient(const SafeZmqClient&) = delete;
    SafeZmqClient& operator=(const SafeZmqClient&) = delete;
    
    /**
     * @brief 发送字符串消息
     * @param message 要发送的消息
     * @throws ZmqException 发送失败时
     */
    void SendString(const QString& message);
    
    /**
     * @brief 接收字符串消息
     * @return 接收到的消息
     * @throws ZmqException 接收失败时
     */
    QString ReceiveString();
    
    /**
     * @brief 发送JSON对象
     * @param json 要发送的JSON对象
     * @throws ZmqException 发送失败时
     */
    void SendJson(const QJsonObject& json);
    
    /**
     * @brief 接收JSON对象
     * @return 接收到的JSON对象
     * @throws ZmqException 接收失败时
     */
    QJsonObject ReceiveJson();
    
    /**
     * @brief 发送请求并接收响应（字符串版本）
     * @param request 请求消息
     * @return 响应消息
     * @throws ZmqException 操作失败时
     */
    QString SendRequest(const QString& request);
    
    /**
     * @brief 发送请求并接收响应（JSON版本）
     * @param request 请求JSON对象
     * @return 响应JSON对象
     * @throws ZmqException 操作失败时
     */
    QJsonObject SendRequest(const QJsonObject& request);
    
    /**
     * @brief 检查连接是否有效
     * @return true如果连接有效
     */
    bool IsConnected() const;
    
    /**
     * @brief 设置超时时间
     * @param timeout_ms 超时时间（毫秒）
     */
    void SetTimeout(int timeout_ms);
    
    /**
     * @brief 重新连接
     * @throws ZmqException 连接失败时
     */
    void Reconnect();

private:
    void* context_;           // ZMQ context
    void* socket_;            // ZMQ socket
    QString endpoint_;        // 连接端点
    int timeout_ms_;          // 超时时间
    std::vector<char> buffer_; // 动态接收缓冲区
    
    /**
     * @brief 初始化连接
     */
    void Initialize();
    
    /**
     * @brief 清理资源
     */
    void Cleanup();
    
    /**
     * @brief 检查ZMQ操作结果
     * @param result ZMQ函数返回值
     * @param operation 操作名称
     * @throws ZmqException 操作失败时
     */
    void CheckResult(int result, const QString& operation);
    
    /**
     * @brief 确保缓冲区足够大
     * @param min_size 最小大小
     */
    void EnsureBufferSize(size_t min_size);
};

/**
 * @brief 工厂函数：创建用于LLM规则代理的客户端
 * @param endpoint 服务器端点
 * @return 智能指针管理的客户端实例
 */
std::unique_ptr<SafeZmqClient> CreateLLMClient(const QString& endpoint);

} // namespace MQ

#endif // MQ_SAFE_ZMQ_H
