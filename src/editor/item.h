#ifndef EDITOR_ITEM_H
#define EDITOR_ITEM_H

#include <QGraphicsPathItem>

#include "brush_map.h"

namespace Editor {
class Item : public QObject, public QGraphicsPathItem {
  Q_OBJECT

 public:
  Item(DataBase::ItemType type, DataBase::Layer layer, const QString& name,
       QGraphicsItem* parent = nullptr);
  Item(DataBase::ItemType type, DataBase::Layer layer, const QString& name,
       const QPainterPath& path, QGraphicsItem* parent = nullptr);
  ~Item() override;

  void paint(QPainter* painter, const QStyleOptionGraphicsItem* option,
                     QWidget* widget = nullptr) override;

 signals:
  void SigPositionChanged(QString name,  QPointF pos);
  void SigSelectedChanged( QString name, bool is_selected);

 protected:
  QVariant itemChange(GraphicsItemChange change,
                      const QVariant& value) override;
 private:
  QString name_;
  BrushMap brush_map_;
  DataBase::Layer layer_;
  DataBase::ItemType type_;
};
}  // namespace Editor

#endif  //EDITOR_ITEM_H