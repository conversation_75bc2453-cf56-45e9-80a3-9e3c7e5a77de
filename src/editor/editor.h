#ifndef EDITOR_EDITOR_H
#define EDITOR_EDITOR_H

#include <QGraphicsView>
#include <QVector>
#include <memory>

#include "Item.h"
#include "component.h"

namespace Editor {

class Editor : public QGraphicsView {
  Q_OBJECT
 public:
  explicit Editor(QWidget* parent = nullptr);
  ~Editor() override;

  void AddItem(Item* item);
  Item* AddItem(const std::shared_ptr<DataBase::Component>& comp);
  void Clear();
  void ResizeView();
  void ResetItems(const QVector<std::shared_ptr<DataBase::Component>> &comps);

 signals:
  void SigMouseMove(QPointF pos);

 protected:
  void resizeEvent(QResizeEvent* event) override;
  void wheelEvent(QWheelEvent* event) override;
  void mouseMoveEvent(QMouseEvent* event) override;

 private:
  QGraphicsScene* scene_;

  void AddTextItem(QGraphicsItem* parent, const QString& text,
                   const QPointF& pos);
};
}  // namespace Editor

#endif  //EDITOR_EDITOR_H